package com.pig4cloud.pigx.common.core.util;

import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * 图片压缩工具类
 */
public class ImageCompressUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageCompressUtil.class);

    /**
     * 将图片压缩到目标分辨率
     * 
     * @param inputImagePath  原始图片路径
     * @param outputImagePath 输出图片路径
     * @param targetWidth     目标宽度
     * @param targetHeight    目标高度
     * @param preserveRatio   是否保持原始宽高比
     */
    public static void resizeImage(String inputImagePath, String outputImagePath,
            int targetWidth, int targetHeight, boolean preserveRatio) {
        try {
            // 读取原始图片
            File inputFile = new File(inputImagePath);
            BufferedImage originalImage = ImageIO.read(inputFile);
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            // 计算新的尺寸
            int newWidth = targetWidth;
            int newHeight = targetHeight;
            // 如果需要保持宽高比
            if (preserveRatio) {
                // 计算宽高比
                double ratio = (double) originalWidth / (double) originalHeight;
                // 根据目标宽度计算高度
                if (targetWidth / ratio <= targetHeight) {
                    newHeight = (int) (targetWidth / ratio);
                } else {
                    // 根据目标高度计算宽度
                    newWidth = (int) (targetHeight * ratio);
                }
            }
            // 创建新的图片
            BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, originalImage.getType());
            // 绘制调整大小后的图片
            Graphics2D g2d = resizedImage.createGraphics();
            // 设置渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            // 绘制图片
            g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
            g2d.dispose();
            // 获取输出文件的格式
            String formatName = outputImagePath.substring(outputImagePath.lastIndexOf(".") + 1);
            //确保目标路径存在，如果不存在则创建
            File outputFile = new File(outputImagePath);
            if (!outputFile.getParentFile().exists()) {
                outputFile.getParentFile().mkdirs();
            }
            // 保存调整大小后的图片
            ImageIO.write(resizedImage, formatName, new File(outputImagePath));
            LOGGER.info("图片已成功调整大小: " + outputImagePath);
            LOGGER.info("原始分辨率: " + originalWidth + "x" + originalHeight);
            LOGGER.info("调整后分辨率: " + newWidth + "x" + newHeight);
        } catch (IOException e) {
            LOGGER.error("调整图片大小时发生错误", e);
        }
    }

    /**
     * 读取图片并识别分辨率
     * 
     * @return
     */
    public static Pair<Integer, Integer> read(String imagePath) {
        File file = new File(imagePath);
        if (!file.exists()) {
            return null;
        }
        File imageFile = new File(imagePath);
        BufferedImage image = null;
        try {
            image = ImageIO.read(imageFile);
        } catch (IOException e) {
            LOGGER.error("读取图片失败", e);
            return null;
        }
        // 获取图片分辨率
        int width = image.getWidth();
        int height = image.getHeight();
        return Pair.of(width, height);
    }

    public static void main(String[] args) {
        // 示例用法
        String inputImagePath = "/usr/wds/static/uploads/1/file/2025/05/Capture001.png";
        String outputImagePath = "/usr/wds/static/uploads/1/file/2025/05/Capture001002.png";
        // 调整到800x600，保持宽高比
        resizeImage(inputImagePath, outputImagePath, 2560, 1440, false);
        // 调整到固定大小，不保持宽高比
        // resizeImage(inputImagePath, outputImagePath, 800, 600, false);
    }
}
