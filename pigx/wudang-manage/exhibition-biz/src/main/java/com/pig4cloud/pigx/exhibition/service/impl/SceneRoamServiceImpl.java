package com.pig4cloud.pigx.exhibition.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.admin.api.constant.UserDelFlagEnum;
import com.pig4cloud.pigx.exhibition.entity.BuildingResourceEntity;
import com.pig4cloud.pigx.exhibition.entity.BuildingSpaceEntity;
import com.pig4cloud.pigx.exhibition.entity.SceneRoamEntity;
import com.pig4cloud.pigx.exhibition.mapper.BuildingResourceMapper;
import com.pig4cloud.pigx.exhibition.mapper.BuildingSpaceMapper;
import com.pig4cloud.pigx.exhibition.mapper.SceneMapper;
import com.pig4cloud.pigx.exhibition.mapper.SceneRoamMapper;
import com.pig4cloud.pigx.exhibition.requests.building.*;
import com.pig4cloud.pigx.exhibition.service.SceneRoamService;
import com.pig4cloud.pigx.exhibition.vo.building.SceneRoamTreeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 场景漫游
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SceneRoamServiceImpl extends ServiceImpl<SceneRoamMapper, SceneRoamEntity> implements SceneRoamService {

    /**
     * 根节点父ID
     */
    public static final Long PARENT_ID = 0L;

    private final SceneMapper sceneMapper;
    private final BuildingSpaceMapper buildingSpaceMapper;
    private final BuildingResourceMapper buildingResourceMapper;

    private SceneRoamEntity selectByRoamId(Long roamId) {
        Wrapper<SceneRoamEntity> queryWrapper = Wrappers.<SceneRoamEntity>lambdaQuery()
                .eq(SceneRoamEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                .eq(SceneRoamEntity::getRoamId, roamId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<Tree<String>> selectSceneRoamTree(Long sceneId, Long parentId, String nodeName) {
        // 根据名称模糊匹配查询
        List<SceneRoamEntity> roamList = baseMapper.selectList(
                Wrappers.<SceneRoamEntity>lambdaQuery()
                        .select(SceneRoamEntity::getRoamId, SceneRoamEntity::getParentId, SceneRoamEntity::getSourceDataId, SceneRoamEntity::getNodeName, SceneRoamEntity::getSerialNumber)
                        .eq(SceneRoamEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                        .eq(SceneRoamEntity::getSceneId, sceneId)
                        .like(StrUtil.isNotBlank(nodeName), SceneRoamEntity::getNodeName, nodeName));

        List<TreeNode<String>> collect = roamList.stream()
                .sorted(Comparator.comparingInt(SceneRoamEntity::getSerialNumber)).map(x -> {
                    TreeNode<String> treeNode = new TreeNode<>();
                    treeNode.setId(String.valueOf(x.getRoamId()));
                    treeNode.setParentId(String.valueOf(x.getParentId()));
                    treeNode.setName(x.getNodeName());
                    treeNode.setWeight(x.getSerialNumber() == null ? 1 : x.getSerialNumber());

                    Map<String, Object> extra = new HashMap<>(8);
                    extra.put("sourceDataId", String.valueOf(x.getSourceDataId()));
                    treeNode.setExtra(extra);
                    return treeNode;
                }).collect(Collectors.toList());

        // 模糊查询 不组装树结构 直接返回 表格方便编辑
        if (StrUtil.isNotBlank(nodeName)) {
            return collect.stream().map(node -> {
                Tree<String> tree = new Tree<>();
                tree.putAll(node.getExtra());
                BeanUtils.copyProperties(node, tree);
                return tree;
            }).collect(Collectors.toList());
        }

        // 构建树结构
        return TreeUtil.build(collect, String.valueOf(parentId == null ? PARENT_ID : parentId));
    }

    @Override
    public List<SceneRoamTreeVO> selectSceneRoamTree(Long sceneId) {
        // 根据名称模糊匹配查询
        List<SceneRoamEntity> roamList = baseMapper.selectList(
                Wrappers.<SceneRoamEntity>lambdaQuery()
                        .eq(SceneRoamEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                        .eq(SceneRoamEntity::getSceneId, sceneId).orderBy(true, true, SceneRoamEntity::getParentId, SceneRoamEntity::getSerialNumber));

        // 将roamList的值转换为SceneRoamTreeVO树形结构集合，根节点parentId值为0
        List<SceneRoamTreeVO> result = new ArrayList<>();



        return TreeUtil.build(roamList, PARENT_ID);



//        List<TreeNode<String>> collect = buildAllList.stream()
//                .sorted(Comparator.comparingInt(SceneRoamEntity::getSerialNumber)).map(x -> {
//                    TreeNode<String> treeNode = new TreeNode<>();
//                    treeNode.setId(String.valueOf(x.getRoamId()));
//                    treeNode.setParentId(String.valueOf(x.getParentId()));
//                    treeNode.setName(x.getNodeName());
//                    treeNode.setWeight(x.getSerialNumber() == null ? 1 : x.getSerialNumber());
//
//                    Map<String, Object> extra = new HashMap<>(8);
//                    extra.put("sourceDataId", String.valueOf(x.getSourceDataId()));
//                    treeNode.setExtra(extra);
//                    return treeNode;
//                }).collect(Collectors.toList());
//
//        // 模糊查询 不组装树结构 直接返回 表格方便编辑
//        if (StrUtil.isNotBlank(nodeName)) {
//            return collect.stream().map(node -> {
//                Tree<String> tree = new Tree<>();
//                tree.putAll(node.getExtra());
//                BeanUtils.copyProperties(node, tree);
//                return tree;
//            }).collect(Collectors.toList());
//        }
//
//        // 构建树结构
//        return TreeUtil.build(collect, String.valueOf(parentId == null ? PARENT_ID : parentId));
    }


    @Override
    public boolean addRoamFirst(SceneRoamFirstAddRequest addRequest) {
        log.info("新增场景漫游-章节 参数：{}", JSONUtil.toJsonStr(addRequest));
        long sceneCount = sceneMapper.selectCountBySceneId(addRequest.getSceneId());
        if (sceneCount < 1) {
            throw new RuntimeException("新增失败，场景不存在");
        }

        // 同级别名称不能重复
        long countNum = baseMapper.selectCount(
                Wrappers.<SceneRoamEntity>lambdaQuery()
                        .eq(SceneRoamEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                        .eq(SceneRoamEntity::getSceneId, addRequest.getSceneId())
                        .eq(SceneRoamEntity::getParentId, PARENT_ID)
                        .eq(SceneRoamEntity::getNodeName, addRequest.getNodeName()));
        if (countNum > 0) {
            throw new RuntimeException("新增失败，章节名称[" + addRequest.getNodeName() + "]已存在");
        }
        // 数据转换
        SceneRoamEntity entity = new SceneRoamEntity();
        BeanUtils.copyProperties(addRequest, entity);
        entity.setParentId(PARENT_ID);
        entity.setDelFlag(UserDelFlagEnum.NORMAL.getCode());
        entity.setCreateTime(LocalDateTime.now());
        return baseMapper.insert(entity) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addBatchRoamSecond(SceneRoamSecondAddRequest addRequest) {
        log.info("批量新增场景漫游-宫观 参数：{}", JSONUtil.toJsonStr(addRequest));
        long sceneCount = sceneMapper.selectCountBySceneId(addRequest.getSceneId());
        if (sceneCount < 1) {
            throw new RuntimeException("新增失败，场景不存在");
        }

        List<Long> sourceDataIds = addRequest.getSourceDataIdList().stream().map(Long::parseLong).collect(Collectors.toList());
        // 同一个章节下宫观数据，只能添加一次
        List<SceneRoamEntity> buildingNodeList = baseMapper.selectListByBuildingIds(addRequest.getSceneId(), addRequest.getParentId(), sourceDataIds);
        if (buildingNodeList != null && !buildingNodeList.isEmpty()) {
            String sourceDataNames = buildingNodeList.stream().map(SceneRoamEntity::getSourceDataName).collect(Collectors.joining(","));
            throw new RuntimeException("新增失败，宫观[" + sourceDataNames + "]已存在");
        }
        // 查询建筑空间的信息
        List<BuildingSpaceEntity> buildingList = buildingSpaceMapper.selectBatchIds(sourceDataIds);
        SceneRoamEntity entity = null;
        int index = 0;
        for (BuildingSpaceEntity item : buildingList) {
            entity = new SceneRoamEntity();
            entity.setParentId(addRequest.getParentId());
            entity.setSceneId(addRequest.getSceneId());
            entity.setSourceDataId(item.getBuildingId());
            entity.setNodeName(item.getBuildingName());
            entity.setSerialNumber(index++);
            entity.setDelFlag(UserDelFlagEnum.NORMAL.getCode());
            entity.setCreateTime(LocalDateTime.now());
            baseMapper.insert(entity);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addBatchRoamThird(SceneRoamSecondAddRequest addRequest) {
        log.info("批量新增场景漫游-节点 参数：{}", JSONUtil.toJsonStr(addRequest));
        long sceneCount = sceneMapper.selectCountBySceneId(addRequest.getSceneId());
        if (sceneCount < 1) {
            throw new RuntimeException("新增失败，场景不存在");
        }

        List<Long> sourceDataIds = addRequest.getSourceDataIdList().stream().map(Long::parseLong).collect(Collectors.toList());
        // 同一个宫观下节点数据，只能添加一次
        List<SceneRoamEntity> nodeList = baseMapper.selectListByResourceIds(addRequest.getSceneId(), addRequest.getParentId(), sourceDataIds);
        if (nodeList != null && !nodeList.isEmpty()) {
            String sourceDataNames = nodeList.stream().map(SceneRoamEntity::getSourceDataName).collect(Collectors.joining(","));
            throw new RuntimeException("新增失败，节点[" + sourceDataNames + "]已存在");
        }
        // 查询建筑空间的信息
        List<BuildingResourceEntity> redourceList = buildingResourceMapper.selectBatchIds(sourceDataIds);
        SceneRoamEntity entity = null;
        int index = 0;
        for (BuildingResourceEntity item : redourceList) {
            entity = new SceneRoamEntity();
            entity.setParentId(addRequest.getParentId());
            entity.setSceneId(addRequest.getSceneId());
            entity.setSourceDataId(item.getResourceId());
            entity.setNodeName(item.getResourceName());
            entity.setCover(item.getCover());
            entity.setSerialNumber(index++);
            entity.setDelFlag(UserDelFlagEnum.NORMAL.getCode());
            entity.setCreateTime(LocalDateTime.now());
            baseMapper.insert(entity);
        }
        return true;
    }

    @Override
    public boolean updateRoamFirst(SceneRoamFirstUpdateRequest updateReq) {
        log.info("修改场景漫游-章节 参数：{}", JSONUtil.toJsonStr(updateReq));
        // 同级别名称不能重复
        long countNum = baseMapper.selectCountCheckNodeNameById(updateReq.getRoamId(), updateReq.getNodeName());
        if (countNum > 0) {
            throw new RuntimeException("修改失败，章节名称[" + updateReq.getNodeName() + "]已存在");
        }
        // 数据转换
        SceneRoamEntity entity = new SceneRoamEntity();
        BeanUtils.copyProperties(updateReq, entity);
        entity.setUpdateTime(LocalDateTime.now());
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoamSecond(SceneRoamSecondUpdateRequest updateReq) {
        log.info("修改场景漫游-宫观 参数：{}", JSONUtil.toJsonStr(updateReq));

        SceneRoamEntity entity = new SceneRoamEntity();
        BeanUtils.copyProperties(updateReq, entity);
        entity.setUpdateTime(LocalDateTime.now());
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoamThird(SceneRoamThirdUpdateRequest updateReq) {
        log.info("修改场景漫游-资源节点 参数：{}", JSONUtil.toJsonStr(updateReq));

        SceneRoamEntity entity = new SceneRoamEntity();
        BeanUtils.copyProperties(updateReq, entity);
        entity.setUpdateTime(LocalDateTime.now());
        // 根据展示详情判断是否为首次选择
        if (StringUtils.hasText(updateReq.getConfigDetail())) {

        }
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    public boolean deleteByRoamId(Long roamId) {
        log.info("删除场景漫游单个节点数据 参数：{}", roamId);

        // 获取场景数据
        SceneRoamEntity oldEntity = selectByRoamId(roamId);
        if (oldEntity == null) {
            return false;
        }

        LambdaUpdateWrapper<SceneRoamEntity> updateWrapper = Wrappers.<SceneRoamEntity>lambdaUpdate()
                .eq(SceneRoamEntity::getRoamId, roamId)
                .set(SceneRoamEntity::getDelFlag, UserDelFlagEnum.DEL.getCode());
        int update = this.baseMapper.update(updateWrapper);
        log.info("删除场景漫游单个节点数据 结果：{}", update);
        return update > 0;
    }

}