package com.pig4cloud.pigx.exhibition.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.exhibition.entity.SceneRoamEntity;
import com.pig4cloud.pigx.exhibition.requests.building.*;

import java.util.List;

public interface SceneRoamService extends IService<SceneRoamEntity> {

    /**
     * 查询场景漫游树形结构
     *
     * @param sceneId
     * @return
     */
    List<Tree<String>> selectSceneRoamTree(Long sceneId);

    /**
     * 查询场景漫游树形结构
     *
     * @param sceneId
     * @param parentId
     * @param nodeName
     * @return
     */
    List<Tree<String>> selectSceneRoamTree(Long sceneId, Long parentId, String nodeName);

    /**
     * 添加场景漫游一级节点（章节节点）
     *
     * @param addRequest
     * @return
     */
    boolean addRoamFirst(SceneRoamFirstAddRequest addRequest);

    /**
     * 批量添加场景漫游二级节点（宫观节点）
     *
     * @param addRequest
     * @return
     */
    boolean addBatchRoamSecond(SceneRoamSecondAddRequest addRequest);

    /**
     * 批量添加场景漫游三级节点（节点）
     *
     * @param addRequest
     * @return
     */
    boolean addBatchRoamThird(SceneRoamSecondAddRequest addRequest);

    /**
     * 修改场景漫游一级节点（章节节点）
     *
     * @param updateReq
     * @return
     */
    boolean updateRoamFirst(SceneRoamFirstUpdateRequest updateReq);

    /**
     * 修改场景漫游二级节点（宫观节点）
     *
     * @param updateReq
     * @return
     */
    boolean updateRoamSecond(SceneRoamSecondUpdateRequest updateReq);

    /**
     * 修改场景漫游三级节点（节点）
     *
     * @param updateReq
     * @return
     */
    boolean updateRoamThird(SceneRoamThirdUpdateRequest updateReq);

    boolean deleteByRoamId(Long roamId);
}