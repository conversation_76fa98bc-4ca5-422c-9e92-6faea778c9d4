package com.pig4cloud.pigx.exhibition.vo.building;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 场景漫游树形结构
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
public class SceneRoamTreeVO implements Serializable {

    /**
     * 节点ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long roamId;

    /**
     * 父节点ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 场景ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sceneId;

    /**
     * 原始数据ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sourceDataId;

    /**
     * 章节名称、宫观名称、节点名称
     */
    private String nodeName;

    /**
     * 章节序号、宫观序号、显示时间（秒）
     */
    private Integer serialNumber;

    /**
     * 章节介绍、讲解内容、节点简介
     */
    private String description;

    /**
     * 章节-序言1
     */
    private String preface1;

    /**
     * 章节-序言2
     */
    private String preface2;

    /**
     * 宫观-视频链接
     */
    private String videoUrl;

    /**
     * 宫观-数字人形象
     */
    private String digitalHuman;

    /**
     * 宫观-是否讲解，0否，1是
     */
    private String explainFlag;

    /**
     * 宫观-是否允许评论，0否，1是
     */
    private String commentFlag;

    /**
     * 宫观-是否展示评论，0否，1是
     */
    private String showCommentFlag;

    /**
     * 宫观-讲解音频
     */
    private String audioUrl;

    /**
     * 节点-节点图片
     */
    private String cover;

    /**
     * 节点-展览方式，1图片，2音频，3视频，4模型，5时间轴，6长卷形式
     */
    private String configType;

    /**
     * 节点-展示方式详情
     */
    private String configDetail;

    /**
     * 子节点
     */
    private List<SceneRoamTreeVO> children;
}