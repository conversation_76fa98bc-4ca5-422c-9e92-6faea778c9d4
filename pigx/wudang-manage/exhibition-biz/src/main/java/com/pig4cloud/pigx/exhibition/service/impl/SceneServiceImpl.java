package com.pig4cloud.pigx.exhibition.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.admin.api.constant.UserDelFlagEnum;
import com.pig4cloud.pigx.admin.api.entity.SysUser;
import com.pig4cloud.pigx.exhibition.common.convert.BaseMapping;
import com.pig4cloud.pigx.exhibition.entity.SceneEntity;
import com.pig4cloud.pigx.exhibition.entity.SceneRoamEntity;
import com.pig4cloud.pigx.exhibition.mapper.SceneMapper;
import com.pig4cloud.pigx.exhibition.mapper.SceneRoamMapper;
import com.pig4cloud.pigx.exhibition.requests.building.*;
import com.pig4cloud.pigx.exhibition.service.SceneService;
import com.pig4cloud.pigx.exhibition.vo.building.SceneVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 场景
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SceneServiceImpl extends ServiceImpl<SceneMapper, SceneEntity> implements SceneService {

    private final BaseMapping baseMapping;

    private final SceneRoamMapper sceneRoamMapper;


    private SceneEntity selectDetailsBySceneId(Long sceneId) {
        Wrapper<SceneEntity> queryWrapper = Wrappers.<SceneEntity>lambdaQuery()
                .eq(SceneEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                .eq(SceneEntity::getSceneId, sceneId);
        SceneEntity scene = baseMapper.selectOne(queryWrapper);

        Wrapper<SceneRoamEntity> roamQW = Wrappers.<SceneRoamEntity>query().lambda()
                .eq(SceneRoamEntity::getSceneId, scene.getSceneId()).eq(SceneRoamEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode());
        List<SceneRoamEntity> list = sceneRoamMapper.selectList(roamQW);


    }

    private SceneEntity selectBySceneId(Long sceneId) {
        Wrapper<SceneEntity> queryWrapper = Wrappers.<SceneEntity>lambdaQuery()
                .eq(SceneEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                .eq(SceneEntity::getSceneId, sceneId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public Page<SceneVO> selectScenePage(ScenePageQueryRequest queryRequest) {
        log.info("场景资源分页 查询参数：{}", JSONUtil.toJsonStr(queryRequest));

        LambdaQueryWrapper<SceneEntity> wrapper = Wrappers.<SceneEntity>lambdaQuery()
                .select(SceneEntity::getSceneId, SceneEntity::getSceneName, SceneEntity::getSceneType, SceneEntity::getStatus,
                        SceneEntity::getScreenQuantity, SceneEntity::getCover, SceneEntity::getDescription, SceneEntity::getCreateBy, SceneEntity::getCreateTime)
                .eq(SceneEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                .like(StringUtils.isNotBlank(queryRequest.getSceneName()), SceneEntity::getSceneName, queryRequest.getSceneName())
                .eq(StringUtils.isNotBlank(queryRequest.getSceneType()), SceneEntity::getSceneType, queryRequest.getSceneType())
                .eq(StringUtils.isNotBlank(queryRequest.getStatus()), SceneEntity::getStatus, queryRequest.getStatus());
        Page<SceneEntity> resourcePage = baseMapper.selectPage(new Page<>(queryRequest.getCurrent(), queryRequest.getSize()), wrapper);

        Page<SceneVO> resultPage = baseMapping.convertPage(resourcePage);
        if (CollectionUtils.isEmpty(resourcePage.getRecords())) {
            return resultPage;
        }
        // 将实体数据转为VO
        List<SceneVO> voList = resourcePage.getRecords().stream().map(x -> {
            SceneVO vo = new SceneVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        resultPage.setRecords(voList);
        return resultPage;
    }

    @Override
    public boolean addScene(SceneAddRequest sceneData) {
        log.info("新增场景 参数：{}", sceneData);
        // 同级别名称不能重复
        Long countNum = baseMapper.selectCount(
                Wrappers.<SceneEntity>lambdaQuery()
                        .eq(SceneEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                        .eq(SceneEntity::getSceneName, sceneData.getSceneName()));
        if (countNum > 0) {
            throw new RuntimeException("新增失败，场景名称[" + sceneData.getSceneName() + "]已存在");
        }
        // 数据转换
        SceneEntity entity = new SceneEntity();
        BeanUtils.copyProperties(sceneData, entity);
        entity.setDelFlag(UserDelFlagEnum.NORMAL.getCode());
        entity.setStatus(1);
        entity.setCreateTime(LocalDateTime.now());
        return baseMapper.insert(entity) > 0;
    }

    @Override
    public boolean updateScene(SceneUpdateRequest sceneUpdate) {
        SceneEntity oldEntity = selectBySceneId(sceneUpdate.getSceneId());
        if (oldEntity == null) {
            throw new RuntimeException("场景信息修改失败。");
        }

        // 同级别名称不能重复
        Long countNum = baseMapper.selectCount(
                Wrappers.<SceneEntity>lambdaQuery()
                        .eq(SceneEntity::getDelFlag, UserDelFlagEnum.NORMAL.getCode())
                        .eq(SceneEntity::getSceneName, sceneUpdate.getSceneName())
                        .ne(SceneEntity::getSceneId, sceneUpdate.getSceneId()));
        if (countNum > 0) {
            throw new RuntimeException("修改失败，场景名称[" + sceneUpdate.getSceneName() + "]已存在");
        }

        // 数据转换
        SceneEntity entity = new SceneEntity();
        BeanUtils.copyProperties(sceneUpdate, entity);
        entity.setUpdateTime(LocalDateTime.now());
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    public boolean updateSceneStatus(SceneStatusUpdateRequest statusUpdate) {
        // 数据转换
        SceneEntity entity = new SceneEntity();
        BeanUtils.copyProperties(statusUpdate, entity);
        entity.setUpdateTime(LocalDateTime.now());
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    public boolean deleteScene(Long sceneId) {
        log.info("删除场景 参数：{}", sceneId);

        // 获取场景数据
        SceneEntity oldEntity = selectBySceneId(sceneId);
        if (oldEntity == null) {
            return false;
        }

        LambdaUpdateWrapper<SceneEntity> updateWrapper = Wrappers.<SceneEntity>lambdaUpdate()
                .eq(SceneEntity::getSceneId, sceneId)
                .set(SceneEntity::getDelFlag, UserDelFlagEnum.DEL.getCode());
        int update = this.baseMapper.update(updateWrapper);
        log.info("删除场景数据 结果：{}", update);
        return update > 0;
    }
}