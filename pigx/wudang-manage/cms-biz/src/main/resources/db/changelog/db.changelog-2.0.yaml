databaseChangeLog:
  - changeSet:
      id: 1646412356256-1
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: org_id_
                  remarks: 组织ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: channel_id_
                  remarks: 栏目ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: user_id_
                  remarks: 创建用户ID
                  type: INT
              - column:
                  name: modified_user_id_
                  remarks: 修改用户ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: publish_date_
                  remarks: 发布日期
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: with_image_
                  remarks: 是否有图片
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: sticky_
                  remarks: 置顶
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: status_
                  remarks: 状态(0:正常)
                  type: SMALLINT
            remarks: 文章表
            tableName: ujcms_article
  - changeSet:
      id: 1646412356256-2
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: comments_
                  remarks: 评论次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: downloads_
                  remarks: 下载次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: favorites_
                  remarks: 收藏次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: ups_
                  remarks: 顶
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: downs_
                  remarks: 踩
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: views_
                  remarks: 浏览次数
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: day_views_
                  remarks: 日浏览次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: week_views_
                  remarks: 周浏览次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: month_views_
                  remarks: 月浏览次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: quarter_views_
                  remarks: 季浏览次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: year_views_
                  remarks: 年浏览次数
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: stat_day_
                  remarks: 统计日
                  type: INT
            remarks: 文章缓冲表
            tableName: ujcms_article_buffer
  - changeSet:
      id: 1646412356256-3
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                  name: article_id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(50)
              - column:
                  name: value_
                  remarks: 值
                  type: MEDIUMTEXT
            remarks: 文章自定义表
            tableName: ujcms_article_custom
  - changeSet:
      id: 1646412356256-4
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: title_
                  remarks: 标题
                  type: VARCHAR(450)
              - column:
                  name: subtitle_
                  remarks: 副标题
                  type: VARCHAR(450)
              - column:
                  name: full_title_
                  remarks: 完整标题
                  type: VARCHAR(450)
              - column:
                  name: alias_
                  remarks: 别名
                  type: VARCHAR(160)
              - column:
                  name: link_url_
                  remarks: 转向链接地址
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: target_blank_
                  remarks: 是否新窗口打开
                  type: TINYINT(3)
              - column:
                  name: seo_keywords_
                  remarks: SEO关键词
                  type: VARCHAR(450)
              - column:
                  name: seo_description_
                  remarks: 摘要
                  type: VARCHAR(3000)
              - column:
                  name: author_
                  remarks: 作者
                  type: VARCHAR(150)
              - column:
                  name: editor_
                  remarks: 编辑
                  type: VARCHAR(150)
              - column:
                  name: source_
                  remarks: 来源
                  type: VARCHAR(150)
              - column:
                  name: offline_date_
                  remarks: 下线日期
                  type: datetime
              - column:
                  name: sticky_date_
                  remarks: 置顶时间
                  type: datetime
              - column:
                  name: image_
                  remarks: 图片
                  type: VARCHAR(255)
              - column:
                  name: video_
                  remarks: 视频
                  type: VARCHAR(255)
              - column:
                  name: video_time_
                  remarks: 视频时长
                  type: VARCHAR(10)
              - column:
                  name: file_
                  remarks: 文件
                  type: VARCHAR(255)
              - column:
                  name: file_name_
                  remarks: 文件名称
                  type: VARCHAR(450)
              - column:
                  name: file_length_
                  remarks: 文件大小
                  type: BIGINT
              - column:
                  name: doc_
                  remarks: 文库
                  type: VARCHAR(255)
              - column:
                  name: doc_name_
                  remarks: 文库名称
                  type: VARCHAR(450)
              - column:
                  name: doc_length_
                  remarks: 文库大小
                  type: BIGINT
              - column:
                  name: article_template_
                  remarks: 独立模板
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: allow_comment_
                  remarks: 是否允许评论
                  type: TINYINT(3)
              - column:
                  name: static_file_
                  remarks: 静态页文件
                  type: VARCHAR(255)
              - column:
                  name: mobile_static_file_
                  remarks: 手机端静态页文件
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  name: created_
                  remarks: 创建日期
                  type: datetime
              - column:
                  name: modified_
                  remarks: 修改日期
                  type: datetime
              - column:
                  name: text_
                  remarks: 正文
                  type: MEDIUMTEXT
            remarks: 文章扩展表
            tableName: ujcms_article_ext
  - changeSet:
      id: 1646412356256-5
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                  name: article_id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 文件名称
                  type: VARCHAR(450)
              - column:
                  constraints:
                    nullable: false
                  name: url_
                  remarks: 文件URL
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: length_
                  remarks: 文件大小
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 文章附件集表
            tableName: ujcms_article_file
  - changeSet:
      id: 1646412356256-6
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                  name: article_id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: url_
                  remarks: 图片URL
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  name: description_
                  remarks: 图片描述
                  type: VARCHAR(3000)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 文章图片集表
            tableName: ujcms_article_image
  - changeSet:
      id: 1646412356256-7
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                  name: article_id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: stat_day_
                  remarks: 统计日
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: views_
                  remarks: 浏览次数
                  type: INT
            tableName: ujcms_article_stat
  - changeSet:
      id: 1646412356256-8
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: article_id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: tag_id_
                  remarks: TagID
                  type: INT
              - column:
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 文章与Tag关联表
            tableName: ujcms_article_tag
  - changeSet:
      id: 1646412356256-9
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 附件ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: user_id_
                  remarks: 用户ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 文件名称
                  type: VARCHAR(450)
              - column:
                  constraints:
                    nullable: false
                  name: path_
                  remarks: 存储路径
                  type: VARCHAR(100)
              - column:
                  constraints:
                    nullable: false
                    unique: true
                  name: url_
                  remarks: 访问路径
                  type: VARCHAR(100)
              - column:
                  constraints:
                    nullable: false
                  name: length_
                  remarks: 文件长度
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  name: created_
                  remarks: 创建时间
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: used_
                  remarks: 是否使用
                  type: TINYINT(3)
            remarks: 附件表
            tableName: ujcms_attachment
  - changeSet:
      id: 1646412356256-10
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: attachment_id_
                  remarks: 附件ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: refer_type_
                  remarks: 引用类型
                  type: VARCHAR(20)
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: refer_id_
                  remarks: 引用ID
                  type: INT
            tableName: ujcms_attachment_refer
  - changeSet:
      id: 1646412356256-11
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 区块ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  name: alias_
                  remarks: 别名
                  type: VARCHAR(50)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: scope_
                  remarks: 共享范围(0:本站私有,1:子站点共享,2:全局共享)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: with_description_
                  remarks: 是否有摘要
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: with_image_
                  remarks: 是否有图片
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 300
                  name: image_width_
                  remarks: 图片宽度
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 200
                  name: image_height_
                  remarks: 图片高度
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: with_mobile_image_
                  remarks: 是否有手机端图片
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 300
                  name: mobile_image_width_
                  remarks: 手机端图片宽度
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 200
                  name: mobile_image_height_
                  remarks: 手机端图片高度
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: enabled_
                  remarks: 是否启用
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 区块表
            tableName: ujcms_block
  - changeSet:
      id: 1646412356256-12
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 区块项ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: block_id_
                  remarks: 区块ID
                  type: INT
              - column:
                  name: article_id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: title_
                  remarks: 标题
                  type: VARCHAR(450)
              - column:
                  name: description_
                  remarks: 描述
                  type: VARCHAR(3000)
              - column:
                  name: link_url_
                  remarks: 链接
                  type: VARCHAR(255)
              - column:
                  name: image_
                  remarks: 图片
                  type: VARCHAR(255)
              - column:
                  name: mobile_image_
                  remarks: 手机端图片
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: enabled_
                  remarks: 是否启用
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 区块条目表
            tableName: ujcms_block_item
  - changeSet:
      id: 1646412356256-13
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 栏目ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  name: parent_id_
                  remarks: 上级栏目ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: channel_model_id_
                  remarks: 栏目模型ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: article_model_id_
                  remarks: 文章模型ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  name: alias_
                  remarks: 别名
                  type: VARCHAR(50)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: nav_
                  remarks: 是否导航菜单
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: type_
                  remarks: 类型(1:常规栏目,2:单页栏目,3:转向链接,4:链接到第一篇文章,5:链接到第一个子栏目)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: depth_
                  remarks: 层级
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排列顺序
                  type: SMALLINT
            remarks: 栏目表
            tableName: ujcms_channel
  - changeSet:
      id: 1646412356256-14
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 栏目ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: views_
                  remarks: 浏览次数
                  type: BIGINT
            remarks: 栏目缓冲表
            tableName: ujcms_channel_buffer
  - changeSet:
      id: 1646412356256-15
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                  name: channel_id_
                  remarks: 文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(50)
              - column:
                  name: value_
                  remarks: 值
                  type: MEDIUMTEXT
            remarks: 栏目自定义表
            tableName: ujcms_channel_custom
  - changeSet:
      id: 1646412356256-16
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 栏目ID
                  type: INT
              - column:
                  name: seo_title_
                  remarks: SEO标题
                  type: VARCHAR(450)
              - column:
                  name: seo_keywords_
                  remarks: SEO关键词
                  type: VARCHAR(450)
              - column:
                  name: seo_description_
                  remarks: SEO描述
                  type: VARCHAR(3000)
              - column:
                  name: article_template_
                  remarks: 文章模板
                  type: VARCHAR(255)
              - column:
                  name: channel_template_
                  remarks: 栏目模板
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 20
                  name: page_size_
                  remarks: 每页条数
                  type: SMALLINT
              - column:
                  name: image_
                  remarks: 图片
                  type: VARCHAR(255)
              - column:
                  name: link_url_
                  remarks: 转向链接地址
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: target_blank_
                  remarks: 是否新窗口打开
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: allow_comment_
                  remarks: 是否允许评论
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: allow_contribute_
                  remarks: 是否允许投稿
                  type: TINYINT(3)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: allow_search_
                  remarks: 是否允许搜索
                  type: TINYINT(3)
              - column:
                  name: static_file_
                  remarks: 静态页文件
                  type: VARCHAR(255)
              - column:
                  name: mobile_static_file_
                  remarks: 手机端静态页文件
                  type: VARCHAR(255)
              - column:
                  name: text_
                  remarks: 正文
                  type: MEDIUMTEXT
            remarks: 栏目扩展表
            tableName: ujcms_channel_ext
  - changeSet:
      id: 1646412356256-17
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: channel_id_
                  remarks: 栏目ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: group_id_
                  remarks: 用户组ID
                  type: INT
            remarks: 栏目用户组关联表
            tableName: ujcms_channel_group
  - changeSet:
      id: 1646412356256-18
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: channel_id_
                  remarks: 栏目ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: role_id_
                  remarks: 角色ID
                  type: INT
            remarks: 栏目角色关联表
            tableName: ujcms_channel_role
  - changeSet:
      id: 1646412356256-19
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: ancestor_id_
                  remarks: 祖先ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: descendant_id_
                  remarks: 后代ID
                  type: INT
            remarks: 栏目树形闭包表
            tableName: ujcms_channel_tree
  - changeSet:
      id: 1646412356256-20
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 字典ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: type_id_
                  remarks: 数据字典类型ID
                  type: INT
              - column:
                  name: parent_id_
                  remarks: 上级ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  name: value_
                  remarks: 值
                  type: VARCHAR(50)
              - column:
                  name: remark_
                  remarks: 备注
                  type: VARCHAR(900)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排列顺序
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: enabled_
                  remarks: 是否启用
                  type: TINYINT(3)
            remarks: 字典表
            tableName: ujcms_dict
  - changeSet:
      id: 1646412356256-21
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 数据字典类型ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  name: alias_
                  remarks: 别名
                  type: VARCHAR(50)
              - column:
                  name: remark_
                  remarks: 备注
                  type: VARCHAR(900)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: scope_
                  remarks: 共享范围(0:本站私有,1:子站点共享,2:全局共享)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排列顺序
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: sys_
                  remarks: 是否系统字典
                  type: TINYINT(3)
            remarks: 数据字典类型
            tableName: ujcms_dict_type
  - changeSet:
      id: 1646412356256-22
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: ID
                  type: INT
              - column:
                  name: context_path_
                  remarks: 上下文路径
                  type: VARCHAR(50)
              - column:
                  name: port_
                  remarks: 端口号
                  type: INT
              - column:
                  name: secret_
                  remarks: 随机密钥(可用于生成下载key)
                  type: VARCHAR(32)
              - column:
                  constraints:
                    nullable: false
                  name: default_site_id_
                  remarks: 默认站点ID
                  type: INT
              - column:
                  name: channel_url_
                  remarks: 栏目URL地址
                  type: VARCHAR(50)
              - column:
                  name: article_url_
                  remarks: 文章URL地址
                  type: VARCHAR(50)
              - column:
                  name: upload_settings_
                  remarks: 上传设置
                  type: VARCHAR(1000)
              - column:
                  name: register_settings_
                  remarks: 注册设置
                  type: VARCHAR(1000)
              - column:
                  name: email_settings_
                  remarks: 邮件设置
                  type: VARCHAR(1000)
              - column:
                  name: customs_settings_
                  remarks: 自定义设置
                  type: MEDIUMTEXT
            remarks: 全局配置表
            tableName: ujcms_global
  - changeSet:
      id: 1646412356256-23
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 用户组ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  name: description_
                  remarks: 描述
                  type: VARCHAR(900)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 2
                  name: type_
                  remarks: 类型(1:系统,2:常规,3:IP组)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 用户组表
            tableName: ujcms_group
  - changeSet:
      id: 1646412356256-24
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 模型ID
                  type: INT
              - column:
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  name: type_
                  remarks: 类型(,article:文章,channel:栏目,user:用户,site:站点设置,global:全局设置)
                  type: VARCHAR(30)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: scope_
                  remarks: 共享范围(0:本站私有,1:子站点共享,2:全局共享)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排列顺序
                  type: SMALLINT
              - column:
                  name: mains_
                  remarks: 主字段集
                  type: MEDIUMTEXT
              - column:
                  name: asides_
                  remarks: 右侧字段集
                  type: MEDIUMTEXT
              - column:
                  name: customs_
                  remarks: 自定义字段集
                  type: MEDIUMTEXT
            remarks: 模型表
            tableName: ujcms_model
  - changeSet:
      id: 1646412356256-25
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 组织ID
                  type: INT
              - column:
                  name: parent_id_
                  remarks: 上级组织ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  name: phone_
                  remarks: 电话
                  type: VARCHAR(100)
              - column:
                  name: address_
                  remarks: 地址
                  type: VARCHAR(900)
              - column:
                  name: contacts_
                  remarks: 联系人
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: depth_
                  remarks: 层级
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 组织表
            tableName: ujcms_org
  - changeSet:
      id: 1646412356256-26
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: ancestor_id_
                  remarks: 祖先ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: descendant_id_
                  remarks: 后代ID
                  type: INT
            remarks: 组织树形结构表
            tableName: ujcms_org_tree
  - changeSet:
      id: 1646412356256-27
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 推送ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: from_article_id_
                  remarks: 源文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: to_article_id_
                  remarks: 目标文章ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: from_site_id_
                  remarks: 源站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: to_site_id_
                  remarks: 目标站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: user_id_
                  remarks: 推送用户
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: created_
                  remarks: 推送时间
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: type_
                  remarks: 推送类型(1:复制,2:映射,3:引用)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: scope_
                  remarks: 推送范围(1:站内,2:站外)
                  type: SMALLINT
            remarks: 推送表
            tableName: ujcms_push
  - changeSet:
      id: 1646412356256-28
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 角色ID
                  type: INT
              - column:
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(50)
              - column:
                  name: description_
                  remarks: 描述
                  type: VARCHAR(900)
              - column:
                  name: permission_
                  remarks: 功能权限
                  type: MEDIUMTEXT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: rank_
                  remarks: 等级
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
            remarks: 角色表
            tableName: ujcms_role
  - changeSet:
      id: 1646412356256-29
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: name_
                  remarks: 序列名称(通常为表名)
                  type: VARCHAR(50)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: next_val_
                  remarks: 下一个值
                  type: BIGINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: cache_size_
                  remarks: 缓存数量(大于0时有效，等于0则由程序确定大小)
                  type: INT
            remarks: 主键序列表
            tableName: ujcms_seq
  - changeSet:
      id: 1646412356256-30
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 站点ID
                  type: INT
              - column:
                  name: parent_id_
                  remarks: 上级站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: org_id_
                  remarks: 组织ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: model_id_
                  remarks: 模型ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: storage_id_
                  remarks: 附件发布点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: html_storage_id_
                  remarks: 静态页发布点ID
                  type: INT
              - column:
                  name: mobile_storage_id_
                  remarks: 手机端静态页发布点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  name: protocol_
                  remarks: 协议(http,https)
                  type: VARCHAR(20)
              - column:
                  constraints:
                    nullable: false
                  name: domain_
                  remarks: 域名
                  type: VARCHAR(50)
              - column:
                  name: sub_dir_
                  remarks: 子目录
                  type: VARCHAR(50)
              - column:
                  constraints:
                    nullable: false
                  name: theme_
                  remarks: 主题
                  type: VARCHAR(50)
              - column:
                  constraints:
                    nullable: false
                  name: mobile_theme_
                  remarks: 手机端主题
                  type: VARCHAR(50)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 20
                  name: page_size_
                  remarks: 每页条数
                  type: SMALLINT
              - column:
                  name: logo_
                  remarks: LOGO
                  type: VARCHAR(255)
              - column:
                  name: seo_title_
                  remarks: SEO标题
                  type: VARCHAR(450)
              - column:
                  name: seo_keywords_
                  remarks: SEO关键词
                  type: VARCHAR(450)
              - column:
                  name: seo_description_
                  remarks: SEO描述
                  type: VARCHAR(3000)
              - column:
                  name: static_file_
                  remarks: 静态页文件
                  type: VARCHAR(255)
              - column:
                  name: mobile_static_file_
                  remarks: 手机端静态页文件
                  type: VARCHAR(255)
              - column:
                  name: watermark_settings_
                  remarks: 水印设置
                  type: VARCHAR(1000)
              - column:
                  name: html_settings_
                  remarks: 静态页设置
                  type: VARCHAR(1000)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  name: depth_
                  remarks: 层级
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排序
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: status_
                  remarks: 状态(0:正常,1:关闭)
                  type: SMALLINT
            remarks: 站点表
            tableName: ujcms_site
  - changeSet:
      id: 1646412356256-31
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: views_
                  remarks: 浏览次数
                  type: BIGINT
            remarks: 站点缓冲表
            tableName: ujcms_site_buffer
  - changeSet:
      id: 1646412356256-32
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(50)
              - column:
                  name: value_
                  remarks: 值
                  type: MEDIUMTEXT
            remarks: 站点自定义字符串表
            tableName: ujcms_site_custom
  - changeSet:
      id: 1646412356256-33
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: ancestor_id_
                  remarks: 祖先ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: descendant_id_
                  remarks: 后代ID
                  type: INT
            remarks: 站点树形结构表
            tableName: ujcms_site_tree
  - changeSet:
      id: 1646412356256-34
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 发布点ID
                  type: INT
              - column:
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  name: description_
                  remarks: 描述
                  type: VARCHAR(900)
              - column:
                  constraints:
                    nullable: false
                  name: type_
                  remarks: 类型(1:HTML存储,2:附件存储)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: mode_
                  remarks: 存储模式(0:本地服务器,1:FTP,2:MinIO,3:阿里云,4:腾讯云,5:七牛云)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: scope_
                  remarks: 共享范围(0:本站私有,1:子站点共享,2:全局共享)
                  type: SMALLINT
              - column:
                  name: path_
                  remarks: 存储路径
                  type: VARCHAR(255)
              - column:
                  name: url_
                  remarks: 访问路径
                  type: VARCHAR(255)
              - column:
                  name: attrs_
                  remarks: 属性集
                  type: VARCHAR(1000)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 32767
                  name: order_
                  remarks: 排列顺序
                  type: SMALLINT
            remarks: 存储点表
            tableName: ujcms_storage
  - changeSet:
      id: 1646412356256-35
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: TagID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: user_id_
                  remarks: 创建用户ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  name: description_
                  remarks: 描述
                  type: VARCHAR(3000)
              - column:
                  constraints:
                    nullable: false
                  name: created_
                  remarks: 创建时间
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: refers_
                  remarks: 引用数量
                  type: INT
            remarks: Tag标签表
            tableName: ujcms_tag
  - changeSet:
      id: 1646412356256-36
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 任务ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: site_id_
                  remarks: 站点ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: user_id_
                  remarks: 用户ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: name_
                  remarks: 名称
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  name: begin_date_
                  remarks: 开始时间
                  type: datetime
              - column:
                  name: end_date_
                  remarks: 结束时间
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: current_
                  remarks: 已完成数量
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: total_
                  remarks: 总数量
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: type_
                  remarks: 类型(1:HTML生成,2:全文索引生成)
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: status_
                  remarks: 状态(0:等待,1:运行中,2:出错,3:停止,4:完成)
                  type: SMALLINT
              - column:
                  name: error_info_
                  remarks: 错误信息
                  type: MEDIUMTEXT
            remarks: 任务表
            tableName: ujcms_task
  - changeSet:
      id: 1646412356256-37
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 用户ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: group_id_
                  remarks: 用户组ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: org_id_
                  remarks: 组织ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    unique: true
                  name: username_
                  remarks: 用户名
                  type: VARCHAR(90)
              - column:
                  constraints:
                    nullable: false
                  defaultValue: '0'
                  name: password_
                  remarks: 密码
                  type: VARCHAR(128)
              - column:
                  constraints:
                    nullable: false
                  defaultValue: '0'
                  name: salt_
                  remarks: 密码混淆码
                  type: VARCHAR(32)
              - column:
                  name: email_
                  remarks: 电子邮箱
                  type: VARCHAR(50)
              - column:
                  name: mobile_
                  remarks: 手机号码
                  type: VARCHAR(50)
              - column:
                  name: alias_
                  remarks: 博客地址
                  type: VARCHAR(50)
              - column:
                  name: display_name_
                  remarks: 显示名
                  type: VARCHAR(150)
              - column:
                  name: avatar_
                  remarks: 头像URL
                  type: VARCHAR(255)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 999
                  name: rank_
                  remarks: 等级
                  type: SMALLINT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: status_
                  remarks: 状态(0:正常,1:锁定)
                  type: SMALLINT
            remarks: 用户表
            tableName: ujcms_user
  - changeSet:
      id: 1646412356256-38
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id_
                  remarks: 用户ID
                  type: INT
              - column:
                  name: real_name_
                  remarks: 真实姓名
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  defaultValue: m
                  name: gender_
                  remarks: 性别(m:男,f:女,n:保密)
                  type: CHAR(1)
              - column:
                  name: birthday_
                  remarks: 出生日期
                  type: datetime
              - column:
                  name: location_
                  remarks: 居住地
                  type: VARCHAR(600)
              - column:
                  name: bio_
                  remarks: 自我介绍
                  type: VARCHAR(3000)
              - column:
                  constraints:
                    nullable: false
                  name: created_
                  remarks: 创建日期
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  name: login_date_
                  remarks: 最后登录日期
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  defaultValue: localhost
                  name: login_ip_
                  remarks: 最后登录IP
                  type: VARCHAR(39)
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: login_count_
                  remarks: 登录次数
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  name: error_date_
                  remarks: 登录错误日期
                  type: datetime
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  name: error_count_
                  remarks: 登录错误次数
                  type: INT
            remarks: 用户扩展表
            tableName: ujcms_user_ext
  - changeSet:
      id: 1646412356256-39
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: user_id_
                  remarks: 用户ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: provider_
                  remarks: 提供商
                  type: VARCHAR(20)
              - column:
                  constraints:
                    nullable: false
                  name: openid_
                  remarks: OPEN ID
                  type: VARCHAR(50)
              - column:
                  name: unionid_
                  remarks: 微信统一ID
                  type: VARCHAR(50)
              - column:
                  name: display_name_
                  remarks: 显示名
                  type: VARCHAR(150)
              - column:
                  constraints:
                    nullable: false
                  defaultValue: m
                  name: gender_
                  remarks: 性别(m:男,f:女,n:保密)
                  type: CHAR(1)
              - column:
                  name: avatar_url_
                  remarks: 头像URL
                  type: VARCHAR(255)
              - column:
                  name: large_avatar_url_
                  remarks: 大头像URL
                  type: VARCHAR(255)
            remarks: 用户OpenID表
            tableName: ujcms_user_openid
  - changeSet:
      id: 1646412356256-40
      author: PONY (generated)
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: user_id_
                  remarks: 用户ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: role_id_
                  remarks: 角色ID
                  type: INT
              - column:
                  constraints:
                    nullable: false
                  defaultValueNumeric: 9999
                  name: order_
                  remarks: 角色排序
                  type: SMALLINT
            remarks: 用户角色关联表
            tableName: ujcms_user_role
  - changeSet:
      id: 1646412356256-41
      author: PONY (generated)
      changes:
        - addUniqueConstraint:
            columnNames: article_id_, block_id_
            constraintName: uk_blockitem_block_article
            tableName: ujcms_block_item
  - changeSet:
      id: 1646412356256-42
      author: PONY (generated)
      changes:
        - addUniqueConstraint:
            columnNames: alias_, site_id_
            constraintName: uk_channel_alias_site
            tableName: ujcms_channel
  - changeSet:
      id: 1646412356256-43
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: channel_id_
            indexName: idx_aritcle_channel
            tableName: ujcms_article
  - changeSet:
      id: 1646412356256-44
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: org_id_
            indexName: idx_article_org
            tableName: ujcms_article
  - changeSet:
      id: 1646412356256-45
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: publish_date_
            indexName: idx_article_publish_date
            tableName: ujcms_article
  - changeSet:
      id: 1646412356256-46
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id_
            indexName: idx_article_user
            tableName: ujcms_article
  - changeSet:
      id: 1646412356256-47
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: modified_user_id_
            indexName: idx_article_user_modified
            tableName: ujcms_article
  - changeSet:
      id: 1646412356256-48
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_article_website
            tableName: ujcms_article
  - changeSet:
      id: 1646412356256-49
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: article_id_
            indexName: idx_articlecustom_article
            tableName: ujcms_article_custom
  - changeSet:
      id: 1646412356256-50
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: article_id_
            indexName: idx_articlefile_article
            tableName: ujcms_article_file
  - changeSet:
      id: 1646412356256-51
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: article_id_
            indexName: idx_articleimage_article
            tableName: ujcms_article_image
  - changeSet:
      id: 1646412356256-52
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: article_id_
            indexName: idx_articlestat_article
            tableName: ujcms_article_stat
  - changeSet:
      id: 1646412356256-53
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: tag_id_
            indexName: idx_articletag_tag
            tableName: ujcms_article_tag
  - changeSet:
      id: 1646412356256-54
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_attachment_site
            tableName: ujcms_attachment
  - changeSet:
      id: 1646412356256-55
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id_
            indexName: idx_attachment_user
            tableName: ujcms_attachment
  - changeSet:
      id: 1646412356256-56
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: refer_type_
              - column:
                  name: refer_id_
            indexName: idx_attachmentrefer_type_id
            tableName: ujcms_attachment_refer
  - changeSet:
      id: 1646412356256-57
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_block_site
            tableName: ujcms_block
  - changeSet:
      id: 1646412356256-58
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: block_id_
            indexName: idx_blockitem_block
            tableName: ujcms_block_item
  - changeSet:
      id: 1646412356256-59
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_blockitem_site
            tableName: ujcms_block_item
  - changeSet:
      id: 1646412356256-60
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: article_model_id_
            indexName: idx_channel_model_article
            tableName: ujcms_channel
  - changeSet:
      id: 1646412356256-61
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: channel_model_id_
            indexName: idx_channel_model_channel
            tableName: ujcms_channel
  - changeSet:
      id: 1646412356256-62
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: parent_id_
            indexName: idx_channel_parent
            tableName: ujcms_channel
  - changeSet:
      id: 1646412356256-63
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_channel_site
            tableName: ujcms_channel
  - changeSet:
      id: 1646412356256-64
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: channel_id_
            indexName: idx_channelcustom_channel
            tableName: ujcms_channel_custom
  - changeSet:
      id: 1646412356256-65
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: group_id_
            indexName: idx_channelgroup_group
            tableName: ujcms_channel_group
  - changeSet:
      id: 1646412356256-66
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: role_id_
            indexName: idx_channelrole_role
            tableName: ujcms_channel_role
  - changeSet:
      id: 1646412356256-67
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: descendant_id_
            indexName: idx_channeltree_descendant
            tableName: ujcms_channel_tree
  - changeSet:
      id: 1646412356256-68
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: type_id_
            indexName: idx_dict_dicttype
            tableName: ujcms_dict
  - changeSet:
      id: 1646412356256-69
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: parent_id_
            indexName: idx_dict_parent
            tableName: ujcms_dict
  - changeSet:
      id: 1646412356256-70
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_dicttype_site
            tableName: ujcms_dict_type
  - changeSet:
      id: 1646412356256-71
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: parent_id_
            indexName: idx_org_parent
            tableName: ujcms_org
  - changeSet:
      id: 1646412356256-72
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: descendant_id_
            indexName: idx_orgtree_descendant
            tableName: ujcms_org_tree
  - changeSet:
      id: 1646412356256-73
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: from_article_id_
            indexName: idx_push_article_from
            tableName: ujcms_push
  - changeSet:
      id: 1646412356256-74
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: to_article_id_
            indexName: idx_push_article_to
            tableName: ujcms_push
  - changeSet:
      id: 1646412356256-75
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: from_site_id_
            indexName: idx_push_site_from
            tableName: ujcms_push
  - changeSet:
      id: 1646412356256-76
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: to_site_id_
            indexName: idx_push_site_to
            tableName: ujcms_push
  - changeSet:
      id: 1646412356256-77
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id_
            indexName: idx_push_user
            tableName: ujcms_push
  - changeSet:
      id: 1646412356256-78
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_role_site
            tableName: ujcms_role
  - changeSet:
      id: 1646412356256-79
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: model_id_
            indexName: idx_site_model
            tableName: ujcms_site
  - changeSet:
      id: 1646412356256-80
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: org_id_
            indexName: idx_site_org
            tableName: ujcms_site
  - changeSet:
      id: 1646412356256-81
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: parent_id_
            indexName: idx_site_parent
            tableName: ujcms_site
  - changeSet:
      id: 1646412356256-82
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: storage_id_
            indexName: idx_site_storage
            tableName: ujcms_site
  - changeSet:
      id: 1646412356256-83
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: html_storage_id_
            indexName: idx_site_storage_html
            tableName: ujcms_site
  - changeSet:
      id: 1646412356256-84
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: mobile_storage_id_
            indexName: idx_site_storage_mobile
            tableName: ujcms_site
  - changeSet:
      id: 1646412356256-85
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_sitecustom_site
            tableName: ujcms_site_custom
  - changeSet:
      id: 1646412356256-86
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: descendant_id_
            indexName: idx_sitetree_descendant
            tableName: ujcms_site_tree
  - changeSet:
      id: 1646412356256-87
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_tag_site
            tableName: ujcms_tag
  - changeSet:
      id: 1646412356256-88
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id_
            indexName: idx_tag_user
            tableName: ujcms_tag
  - changeSet:
      id: 1646412356256-89
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: site_id_
            indexName: idx_task_site
            tableName: ujcms_task
  - changeSet:
      id: 1646412356256-90
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: user_id_
            indexName: idx_task_user
            tableName: ujcms_task
  - changeSet:
      id: 1646412356256-91
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: group_id_
            indexName: idx_user_group
            tableName: ujcms_user
  - changeSet:
      id: 1646412356256-92
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: org_id_
            indexName: idx_user_org
            tableName: ujcms_user
  - changeSet:
      id: 1646412356256-93
      author: PONY (generated)
      changes:
        - createIndex:
            columns:
              - column:
                  name: role_id_
            indexName: idx_userrole_role
            tableName: ujcms_user_role
  - changeSet:
      id: 1646412356256-94
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: channel_id_
            baseTableName: ujcms_article
            constraintName: fk_article_channel
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-95
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: org_id_
            baseTableName: ujcms_article
            constraintName: fk_article_org
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_org
            validate: true
  - changeSet:
      id: 1646412356256-96
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: user_id_
            baseTableName: ujcms_article
            constraintName: fk_article_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-97
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: modified_user_id_
            baseTableName: ujcms_article
            constraintName: fk_article_user_modified
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-98
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_article
            constraintName: fk_article_website
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-99
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: id_
            baseTableName: ujcms_article_buffer
            constraintName: fk_articlebuffer_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-100
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: article_id_
            baseTableName: ujcms_article_custom
            constraintName: fk_articlecustom_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-101
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: id_
            baseTableName: ujcms_article_ext
            constraintName: fk_articleext_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-102
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: article_id_
            baseTableName: ujcms_article_file
            constraintName: fk_articlefile_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-103
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: article_id_
            baseTableName: ujcms_article_image
            constraintName: fk_articleimage_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-104
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: article_id_
            baseTableName: ujcms_article_stat
            constraintName: fk_articlestat_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-105
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: article_id_
            baseTableName: ujcms_article_tag
            constraintName: fk_articletag_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-106
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: tag_id_
            baseTableName: ujcms_article_tag
            constraintName: fk_articletag_tag
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_tag
            validate: true
  - changeSet:
      id: 1646412356256-107
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_attachment
            constraintName: fk_attachment_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-108
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: user_id_
            baseTableName: ujcms_attachment
            constraintName: fk_attachment_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-109
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: attachment_id_
            baseTableName: ujcms_attachment_refer
            constraintName: fk_attachmentrefer_attachment
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_attachment
            validate: true
  - changeSet:
      id: 1646412356256-110
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_block
            constraintName: fk_block_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-111
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: article_id_
            baseTableName: ujcms_block_item
            constraintName: fk_blockitem_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-112
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: block_id_
            baseTableName: ujcms_block_item
            constraintName: fk_blockitem_block
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_block
            validate: true
  - changeSet:
      id: 1646412356256-113
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_block_item
            constraintName: fk_blockitem_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-114
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: channel_id_
            baseTableName: ujcms_channel_custom
            constraintName: fk_channel_custom
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-115
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: article_model_id_
            baseTableName: ujcms_channel
            constraintName: fk_channel_model_article
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_model
            validate: true
  - changeSet:
      id: 1646412356256-116
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: channel_model_id_
            baseTableName: ujcms_channel
            constraintName: fk_channel_model_channel
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_model
            validate: true
  - changeSet:
      id: 1646412356256-117
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: parent_id_
            baseTableName: ujcms_channel
            constraintName: fk_channel_parent
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-118
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_channel
            constraintName: fk_channel_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-119
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: id_
            baseTableName: ujcms_channel_buffer
            constraintName: fk_channelbuffer_channel
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-120
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: id_
            baseTableName: ujcms_channel_ext
            constraintName: fk_channelext_channel
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-121
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: channel_id_
            baseTableName: ujcms_channel_group
            constraintName: fk_channelgroup_channel
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-122
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: group_id_
            baseTableName: ujcms_channel_group
            constraintName: fk_channelgroup_group
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_group
            validate: true
  - changeSet:
      id: 1646412356256-123
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: channel_id_
            baseTableName: ujcms_channel_role
            constraintName: fk_channelrole_channel
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-124
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: role_id_
            baseTableName: ujcms_channel_role
            constraintName: fk_channelrole_role
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_role
            validate: true
  - changeSet:
      id: 1646412356256-125
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: ancestor_id_
            baseTableName: ujcms_channel_tree
            constraintName: fk_channeltree_ancestor
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-126
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: descendant_id_
            baseTableName: ujcms_channel_tree
            constraintName: fk_channeltree_descendant
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_channel
            validate: true
  - changeSet:
      id: 1646412356256-127
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: type_id_
            baseTableName: ujcms_dict
            constraintName: fk_dict_dicttype
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_dict_type
            validate: true
  - changeSet:
      id: 1646412356256-128
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: parent_id_
            baseTableName: ujcms_dict
            constraintName: fk_dict_parent
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_dict
            validate: true
  - changeSet:
      id: 1646412356256-129
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_dict_type
            constraintName: fk_dicttype_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-130
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: parent_id_
            baseTableName: ujcms_org
            constraintName: fk_org_parent
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_org
            validate: true
  - changeSet:
      id: 1646412356256-131
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: ancestor_id_
            baseTableName: ujcms_org_tree
            constraintName: fk_orgtree_ancestor
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_org
            validate: true
  - changeSet:
      id: 1646412356256-132
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: descendant_id_
            baseTableName: ujcms_org_tree
            constraintName: fk_orgtree_descendant
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_org
            validate: true
  - changeSet:
      id: 1646412356256-133
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: from_article_id_
            baseTableName: ujcms_push
            constraintName: fk_push_article_from
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-134
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: to_article_id_
            baseTableName: ujcms_push
            constraintName: fk_push_article_to
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_article
            validate: true
  - changeSet:
      id: 1646412356256-135
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: from_site_id_
            baseTableName: ujcms_push
            constraintName: fk_push_site_from
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-136
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: to_site_id_
            baseTableName: ujcms_push
            constraintName: fk_push_site_to
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-137
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: user_id_
            baseTableName: ujcms_push
            constraintName: fk_push_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-138
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_role
            constraintName: fk_role_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-139
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: model_id_
            baseTableName: ujcms_site
            constraintName: fk_site_model
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_model
            validate: true
  - changeSet:
      id: 1646412356256-140
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: org_id_
            baseTableName: ujcms_site
            constraintName: fk_site_org
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_org
            validate: true
  - changeSet:
      id: 1646412356256-141
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: parent_id_
            baseTableName: ujcms_site
            constraintName: fk_site_parent
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-142
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: storage_id_
            baseTableName: ujcms_site
            constraintName: fk_site_storage
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_storage
            validate: true
  - changeSet:
      id: 1646412356256-143
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: html_storage_id_
            baseTableName: ujcms_site
            constraintName: fk_site_storage_html
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_storage
            validate: true
  - changeSet:
      id: 1646412356256-144
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: mobile_storage_id_
            baseTableName: ujcms_site
            constraintName: fk_site_storage_mobile
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_storage
            validate: true
  - changeSet:
      id: 1646412356256-145
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: id_
            baseTableName: ujcms_site_buffer
            constraintName: fk_sitebuffer_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-146
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_site_custom
            constraintName: fk_sitecustom_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-147
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: ancestor_id_
            baseTableName: ujcms_site_tree
            constraintName: fk_sitetree_ancestor
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-148
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: descendant_id_
            baseTableName: ujcms_site_tree
            constraintName: fk_sitetree_descendant
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-149
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_tag
            constraintName: fk_tag_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-150
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: user_id_
            baseTableName: ujcms_tag
            constraintName: fk_tag_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-151
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: site_id_
            baseTableName: ujcms_task
            constraintName: fk_task_site
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_site
            validate: true
  - changeSet:
      id: 1646412356256-152
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: user_id_
            baseTableName: ujcms_task
            constraintName: fk_task_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-153
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: group_id_
            baseTableName: ujcms_user
            constraintName: fk_user_group
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_group
            validate: true
  - changeSet:
      id: 1646412356256-154
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: org_id_
            baseTableName: ujcms_user
            constraintName: fk_user_org
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_org
            validate: true
  - changeSet:
      id: 1646412356256-155
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: id_
            baseTableName: ujcms_user_ext
            constraintName: fk_userext_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-156
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: user_id_
            baseTableName: ujcms_user_openid
            constraintName: fk_useropenid_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 1646412356256-157
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: role_id_
            baseTableName: ujcms_user_role
            constraintName: fk_userrole_role
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_role
            validate: true
  - changeSet:
      id: 1646412356256-158
      author: PONY (generated)
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: user_id_
            baseTableName: ujcms_user_role
            constraintName: fk_userrole_user
            deferrable: false
            initiallyDeferred: false
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedColumnNames: id_
            referencedTableName: ujcms_user
            validate: true
  - changeSet:
      id: 2.0
      author: PROJECT-VERSION
      changes:
        - tagDatabase:
            tag: v2.0