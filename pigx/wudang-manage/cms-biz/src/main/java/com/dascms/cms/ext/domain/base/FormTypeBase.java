package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class FormTypeBase extends Model<FormTypeBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "form_type";

    /**
     * 表单类型ID
     */
    @NotNull
    @Schema(description="表单类型ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @NotNull
    @Schema(description="站点ID")
    private Long siteId = 0L;

    /**
     * 模型ID
     */
    @NotNull
    @Schema(description="模型ID")
    private Long modelId = 0L;

    /**
     * 流程标识
     */
    @Length(max = 50)
    @Nullable
    @Schema(description="流程标识")
    private String processKey;

    /**
     * 名称
     */
    @Length(max = 50)
    @NotNull
    @Schema(description="名称")
    private String name = "";

    /**
     * 列表模板
     */
    @Length(max = 255)
    @Nullable
    @Schema(description="列表模板")
    private String listTemplate;

    /**
     * 详情模板
     */
    @Length(max = 255)
    @Nullable
    @Schema(description="详情模板")
    private String itemTemplate;

    /**
     * 模式(0:前台游客,1:前台登录用户,2:仅后台用户)
     */
    @NotNull
    @Schema(description="模式(0:前台游客,1:前台登录用户,2:仅后台用户)")
    private Short mode = 2;

    /**
     * 前台可查看
     */
    @NotNull
    @Schema(description="前台可查看")
    private Boolean viewable = false;

    /**
     * 排列顺序
     */
    @NotNull
    @Schema(description="排列顺序")
    private Long order = 0L;

    /**
     * 是否启用
     */
    @NotNull
    @Schema(description="是否启用")
    private Boolean enabled = true;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getModelId() {
        return modelId;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    @Nullable
    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(@Nullable String processKey) {
        this.processKey = processKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Nullable
    public String getListTemplate() {
        return listTemplate;
    }

    public void setListTemplate(@Nullable String listTemplate) {
        this.listTemplate = listTemplate;
    }

    @Nullable
    public String getItemTemplate() {
        return itemTemplate;
    }

    public void setItemTemplate(@Nullable String itemTemplate) {
        this.itemTemplate = itemTemplate;
    }

    public Short getMode() {
        return mode;
    }

    public void setMode(Short mode) {
        this.mode = mode;
    }

    public Boolean getViewable() {
        return viewable;
    }

    public void setViewable(Boolean viewable) {
        this.viewable = viewable;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}