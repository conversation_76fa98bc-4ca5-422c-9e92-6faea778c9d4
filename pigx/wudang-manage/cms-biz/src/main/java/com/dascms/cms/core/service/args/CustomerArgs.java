package com.dascms.cms.core.service.args;

import com.dascms.cms.core.web.support.BasePage;
import lombok.Data;

@Data
public class CustomerArgs extends BasePage {
    /**
     * 账号
     */
    private String username;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 实名认证状态
     */
    private Integer verificationIdStatus;

    /**
     * id
     */
    private Long id;

    /**
     * 备注
     */
    private String remark;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getVerificationIdStatus() {
        return verificationIdStatus;
    }

    public void setVerificationIdStatus(Integer verificationIdStatus) {
        this.verificationIdStatus = verificationIdStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
