package com.dascms.cms.core.domain.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dascms.cms.core.domain.order.Order;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Getter
@Setter
@TableName("ujcms_material")
public class UjcmsMaterial extends Model<UjcmsMaterial> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件类型1模型 2图片 3视频 4音频 5文档
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 类目（编码，从最顶级开始到最末级），1,2,3,4
     */
    private String catalogNo;

    /**
     * 类目（名称,从最顶级开始到最末级），图片,视频,音频,3D模型
     */
    private String catalogName;

    /**
     * 关联资源名称
     */
    private String relatedSource;

    /**
     * 标签
     */
    private String tag;

    /**
     * 简介
     */
    private String overview;

    /**
     * logo
     */
    private String logo;

    /**
     * 素材文件地址
     */
    private String url;

    /**
     * 版权人
     */
    private String author;

    /**
     * 版权文件
     */
    private String authorFile;

    /**
     * 是否上架0否1是
     */
    private Integer onsale;

    /**
     * 是否首页展示0否1是
     */
    private Integer homePage;

    /**
     * 定价方案id
     */
    private Integer pricingStrategyId;

    /**
     * 定价方案名称
     */
    private String pricingStrategyName;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 素材文件上传的路径
     */
    private String filePath;

    /**
     * 素材关联的资源表名
     */
    private String relatedTableName;

    /**
     * 素材关联的资源id
     */
    private String relateBusinessId;

    /**
     * 创建时间--上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime = new Date();

    /**
     * 收藏次数
     */
    private Integer collectCount = 0;

    /**
     * 下载次数
     */
    private Integer downloadCount = 0;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 规格
     */
    private String norm;

    /**
     * 类目（编码）-二级
     */
    private String secondCatalogNo;

    /**
     * 类目（名称）-二级
     */
    private String secondCatalogName;

    /**
     * 类目（编码）-三级（一级和二级暂时不用单独记录）
     */
    private String oneCatalogNo;

    /**
     * 类目（名称）-三级（一级和二级暂时不用单独记录）
     */
    private String oneCatalogName;

    /**
     * 文件分辨率--宽度
     */
    private Integer width;

    /**
     * 文件分辨率--高度
     */
    private Integer height;

    /**
     * 版权文件
     */
    @TableField(exist = false)
    private List<UjcmsMateialAuthFile> authFiles;

    /**
     * 当前用户是否收藏，默认否0，是1
     */
    @TableField(exist = false)
    private int collected = 0;

    /**
     * 浏览次数
     */
    private Integer accessCount = 0;

    /**
     * 定价方案列表
     */
    @TableField(exist = false)
    private List<UjcmsPricingStrategyRule> pricingStrategyRules;

    /**
     * 可选的定价方案列表
     */
    @TableField(exist = false)
    private List<UjcmsMaterialPrice> ujcmsMaterialPrices;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 授权类型
     */
    @TableField(exist = false)
    private String licenseType;

    /**
     * 文件md5
     */
    private String md5;

    /**
     * 预览url
     */
    private String previewUrl;

    /**
     * 素材关联的订单
     */
    @TableField(exist = false)
    private Order order;

    /**
     * 预览文件存储路径
     */
    private String previewFilePath;
}
