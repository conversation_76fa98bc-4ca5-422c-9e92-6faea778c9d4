package com.dascms.cms.core.wechatpay.request;

import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Signature;
import java.util.Base64;

public class WechatPaySignature {
    /**
     * 获取签名
     *
     * @return
     */
    public static String getSignature(String method, String url, String timestamp, String nonceStr, String body) throws Exception {
        // 2. 拼接签名字符串（格式为：HTTP方法\n请求URI\n时间戳\n随机字符串\n请求报文主体\n）
        String signContent = method + "\n"
                + url + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + body + "\n";

        // 3. 加载私钥（从PEM文件）
        PrivateKey privateKey = loadPrivateKeyFromPem("apiclient_test_key.pem");
        // 4. 计算签名
        String signature = sign(signContent, privateKey);
        return signature;
    }

    public static void main(String[] args) throws Exception {
    }

    /**
     * 从PEM文件加载私钥
     */
    private static PrivateKey loadPrivateKeyFromPem(String pemFilePath) throws Exception {
        // 方法1：使用PKCS8EncodedKeySpec方式加载（适用于PKCS#8格式的PEM）
        try (FileInputStream fis = new FileInputStream(pemFilePath)) {
            // 读取PEM文件内容
            byte[] pemBytes = fis.readAllBytes();
            String pemContent = new String(pemBytes, StandardCharsets.UTF_8);

            // 提取PEM文件中的私钥部分（去掉头尾和换行符）
            String privateKeyPEM = pemContent
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");

            // Base64解码
            byte[] encodedKey = Base64.getDecoder().decode(privateKeyPEM);

            // 创建PKCS8EncodedKeySpec
            java.security.spec.PKCS8EncodedKeySpec keySpec = new java.security.spec.PKCS8EncodedKeySpec(encodedKey);

            // 获取RSA密钥工厂
            java.security.KeyFactory keyFactory = java.security.KeyFactory.getInstance("RSA");

            // 生成私钥
            return keyFactory.generatePrivate(keySpec);
        }
    }

    /**
     * 计算SHA256 with RSA签名并进行Base64编码
     */
    private static String sign(String data, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return Base64.getEncoder().encodeToString(signed);
    }

    /**
     * PKCS1方式加载秘钥，也就是秘钥以-----BEGIN RSA PRIVATE KEY-----开头
     *
     * @param pemFilePath
     * @return
     * @throws Exception
     */
    private static PrivateKey loadPrivateKeyFromPemPKCS1(String pemFilePath) throws Exception {
        try (FileInputStream fis = new FileInputStream(pemFilePath)) {
            // 读取PEM文件内容
            byte[] pemBytes = fis.readAllBytes();
            String pemContent = new String(pemBytes, StandardCharsets.UTF_8);

            // 提取PEM文件中的私钥部分（去掉头尾和换行符）
            String privateKeyPEM = pemContent
                    .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                    .replace("-----END RSA PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");

            // Base64解码
            byte[] encodedKey = Base64.getDecoder().decode(privateKeyPEM);

            // 使用BouncyCastle解析PKCS#1格式的私钥
            // 需要添加BouncyCastle依赖：org.bouncycastle:bcprov-jdk15on
            org.bouncycastle.asn1.pkcs.RSAPrivateKey pkcs1Key = org.bouncycastle.asn1.pkcs.RSAPrivateKey.getInstance(encodedKey);

            // 转换为PKCS#8格式
            java.security.spec.RSAPrivateCrtKeySpec keySpec = new java.security.spec.RSAPrivateCrtKeySpec(
                    pkcs1Key.getModulus(),
                    pkcs1Key.getPublicExponent(),
                    pkcs1Key.getPrivateExponent(),
                    pkcs1Key.getPrime1(),
                    pkcs1Key.getPrime2(),
                    pkcs1Key.getExponent1(),
                    pkcs1Key.getExponent2(),
                    pkcs1Key.getCoefficient()
            );

            // 获取RSA密钥工厂
            java.security.KeyFactory keyFactory = java.security.KeyFactory.getInstance("RSA");

            // 生成私钥
            return keyFactory.generatePrivate(keySpec);
        }
    }
}
