package com.dascms.cms.core.web.directive;

import com.dascms.cms.CmsBackendlication;
import com.dascms.cms.core.domain.Channel;
import com.dascms.cms.core.domain.count.ChannelCount;
import com.dascms.cms.core.service.ArticleService;
import com.dascms.cms.core.service.ChannelService;
import com.dascms.cms.core.service.args.ChannelArgs;
import com.dascms.cms.core.support.Frontends;
import com.dascms.cms.core.web.support.Directives;
import com.dascms.cms.core.web.util.ChannelUtil;
import com.dascms.commons.freemarker.Freemarkers;
import freemarker.core.Environment;
import freemarker.template.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 栏目列表 标签
 *
 * <AUTHOR>
 */
public class ChannelListDirective implements TemplateDirectiveModel {
    /**
     * 站点ID。整型（Integer）
     */
    private static final String SITE_ID = "siteId";
    /**
     * 上级栏目ID。整型（Integer）
     */
    private static final String PARENT_ID = "parentId";
    /**
     * 上级栏目别名。字符串（String）
     */
    private static final String PARENT = "parent";
    /**
     * 是否导航。布尔型（Boolean）
     */
    private static final String IS_NAV = "isNav";
    /**
     * 是否可搜索。布尔型（Boolean）
     */
    private static final String IS_ALLOW_SEARCH = "isAllowSearch";
    /**
     * 是否包含子栏目。布尔型（Boolean）。默认：false
     */
    private static final String IS_INCLUDE_CHILDREN = "isIncludeChildren";

    public static void assemble(ChannelArgs args, Map<String, ?> params, Long defaultSiteId,
                                ChannelService channelService) {
        Long siteId = Directives.getLong(params, SITE_ID, defaultSiteId);
        args.siteId(siteId);
        boolean isIncludeChildren = Directives.getBoolean(params, IS_INCLUDE_CHILDREN, false);
        Long parentId = Directives.getLong(params, PARENT_ID);
        if (parentId == null) {
            String parentAlias = Directives.getString(params, PARENT);
            if (StringUtils.isNotBlank(parentAlias)) {
                Channel parent = channelService.findBySiteIdAndAlias(siteId, parentAlias);
                parentId = parent != null ? parent.getId() : Integer.MIN_VALUE;
            }
        }
        if (parentId != null) {
            if (isIncludeChildren) {
                args.ancestorId(parentId);
            } else {
                args.parentId(parentId);
            }
        } else if (!isIncludeChildren) {
            args.parentIdIsNull();
        }
        Optional.ofNullable(Directives.getBoolean(params, IS_NAV)).ifPresent(args::isNav);
        Optional.ofNullable(Directives.getBoolean(params, IS_ALLOW_SEARCH)).ifPresent(args::isAllowSearch);
        Directives.handleOrderBy(args.getQueryMap(), params);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void execute(Environment env, Map params, TemplateModel[] loopVars, TemplateDirectiveBody body)
            throws TemplateException, IOException {
        Freemarkers.requireLoopVars(loopVars);
        Freemarkers.requireBody(body);
        Long defaultSiteId = Frontends.getSiteId(env);

        ChannelArgs args = ChannelArgs.of(Directives.getQueryMap(params));
        assemble(args, params, defaultSiteId, channelService);
        args.customsQueryMap(Directives.getCustomsQueryMap(params));
        SimpleNumber parentIdObj = (SimpleNumber) params.get("parentId");
        Long parentId = parentIdObj != null ? parentIdObj.getAsNumber().longValue() : null;
        List<Channel> list = selectList(channelService, args, params);
        Object alias = params.get("alias");
        ChannelUtil channelUtil = CmsBackendlication.applicationContext.getBean(ChannelUtil.class);
        assembleCountByParent(list, parentId);
        loopVars[0] = env.getObjectWrapper().wrap(list);
        body.render(env.getOut());
    }


    /**
     * 填充统计结果--汇总到顶层父级id
     *
     * @param list
     */
    private void assembleCountByParent(List<Channel> list, Long parentId) {
        ArticleService articleService = CmsBackendlication.applicationContext.getBean(ArticleService.class);
        List<ChannelCount> channelCounts = articleService.statByChannel();
        List<Long> channelIds = channelCounts.parallelStream().map(ChannelCount::getChannelId).distinct().collect(Collectors.toList());
        //存放每个channelid对应的顶层父级id
        Map<Long, Long> parentIdMap = new HashMap<>();
        //执行完这个之后，parentIdMap里面包含了channelid和所有层级的父级id
        getParentIdMap(channelIds, parentIdMap);
        //合并
        Set<Long> mergeIds = list.stream().map(Channel::getId).collect(Collectors.toSet());
//        Set<Long> mergeIds = Stream.of(parentId).collect(Collectors.toSet());
        parentIdMap = mergeMap(parentIdMap, mergeIds);
        for (ChannelCount channelCount : channelCounts) {
            channelCount.setTopParentId(parentIdMap.get(channelCount.getChannelId()));
        }
        //按照父级id汇总的同时，把自己的也放进去
        Map<Long, Integer> channelCountMap = new HashMap<>();
        for (ChannelCount channelCount : channelCounts) {
            if (channelCountMap.get(channelCount.getTopParentId()) == null) {
                channelCountMap.put(channelCount.getTopParentId(), channelCount.getCount());
            } else {
                channelCountMap.put(channelCount.getTopParentId(), channelCountMap.get(channelCount.getTopParentId()) + channelCount.getCount());
            }
            channelCountMap.put(channelCount.getChannelId(), channelCount.getCount());
        }
        for (Channel channel : list) {
            channel.countResult = channelCountMap.get(channel.getId());
        }
    }

    /**
     * 合并map
     */
    private Map<Long, Long> mergeMap(Map<Long, Long> parentIdMap, Set<Long> mergeIds) {
        // 创建一个用于存储每个键最终对应值的辅助函数
        Map<Long, Long> finalValues = new HashMap<>();
        for (Map.Entry<Long, Long> entry : parentIdMap.entrySet()) {
            Long current = entry.getKey();
            Long finalValue = entry.getValue();
            while (parentIdMap.containsKey(finalValue)) {
                if (mergeIds.contains(finalValue)) {
                    break;
                }
                finalValue = parentIdMap.get(finalValue);
            }
            // Store the final parent found
            finalValues.put(current, finalValue);
        }
        return finalValues;
    }

    /**
     * 获取顶层父级id
     *
     * @param parentIdMap
     */
    private void getParentIdMap(List<Long> channelIds, Map<Long, Long> parentIdMap) {
        List<Channel> channels = channelService.getParentIdsByIds(channelIds);
        Map<Long, Long> collect = channelIds.parallelStream().collect(Collectors.toMap(channelId -> channelId, channelId -> channelId, (v1, v2) -> v2));
        List<Long> parentIds = new LinkedList<>();
        for (Channel channel : channels) {
            if (channel.getParentId() != null) {
                parentIds.add(channel.getParentId());
                collect.put(channel.getId(), channel.getParentId());
            }
        }
        //过滤掉key和value相等的键值对
        collect = collect.entrySet().stream().parallel().filter(entry -> !entry.getKey().equals(entry.getValue())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (CollectionUtils.isNotEmpty(parentIds)) {
            getParentIdMap(parentIds, parentIdMap);
            parentIdMap.putAll(collect);
        }
    }

    public static List<Channel> selectList(ChannelService channelService, ChannelArgs args, Map<String, String> params) {
        int offset = Directives.getOffset(params);
        int limit = Directives.getLimit(params);
        return channelService.selectList(args, offset, limit);
    }

    private final ChannelService channelService;

    public ChannelListDirective(ChannelService channelService) {
        this.channelService = channelService;
    }
}
