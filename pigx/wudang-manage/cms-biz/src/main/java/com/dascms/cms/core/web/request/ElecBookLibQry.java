package com.dascms.cms.core.web.request;

import com.dascms.cms.core.web.support.BasePage;
import lombok.Data;

/**
 * 电子图书库查询参数封装
 */
@Data
public class ElecBookLibQry extends BasePage {
    //栏目别名
    private String channelAlias;
    //标题
    private String title;
    //作者
    private String author;
    //出版时间
    private String publicationTime;
    //栏目id
    private Long channelId;

    public String getChannelAlias() {
        return channelAlias;
    }

    public void setChannelAlias(String channelAlias) {
        this.channelAlias = channelAlias;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getPublicationTime() {
        return publicationTime;
    }

    public void setPublicationTime(String publicationTime) {
        this.publicationTime = publicationTime;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }
}
