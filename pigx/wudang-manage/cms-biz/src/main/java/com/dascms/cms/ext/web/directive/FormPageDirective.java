package com.dascms.cms.ext.web.directive;

import com.dascms.cms.ext.service.FormService;
import com.dascms.cms.ext.service.FormTypeService;
import freemarker.core.Environment;
import freemarker.template.TemplateDirectiveBody;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;

import java.io.IOException;
import java.util.Map;

/**
 * 表单列表 标签
 *
 * <AUTHOR>
 */
public class FormPageDirective extends FormListDirective {
    @SuppressWarnings("unchecked")
    @Override
    public void execute(Environment env, Map params, TemplateModel[] loopVars, TemplateDirectiveBody body)
            throws TemplateException, IOException {
        doExecute(env, params, loopVars, body, true);
    }

    public FormPageDirective(FormService service, FormTypeService typeService) {
        super(service, typeService);
    }
}
