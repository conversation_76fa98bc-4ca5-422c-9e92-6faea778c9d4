package com.dascms.cms.core.web.frontend;

import com.dascms.cms.core.domain.Article;
import com.dascms.cms.core.domain.Channel;
import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.service.ArticleService;
import com.dascms.cms.core.service.ChannelService;
import com.dascms.cms.core.service.UserLoginRecordService;
import com.dascms.cms.core.service.UserService;
import com.dascms.cms.core.support.Constants;
import com.dascms.cms.core.support.LoginUserUtil;
import com.dascms.cms.core.web.request.ElecBookLibQry;
import com.pig4cloud.pigx.common.core.util.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;

@Controller
@RequestMapping("front")
public class FrontController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FrontController.class);

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ArticleService articleService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserLoginRecordService userLoginRecordService;

    /**
     * 根据别名查看栏目的子目录
     *
     * @return
     */
    @CrossOrigin(origins = "*")
    @RequestMapping("childChannels")
    @ResponseBody
    public List<Channel> childChannels(@RequestParam String alias) {
        return channelService.childChannels(alias);
    }

    /**
     * 根据栏目id查询文章列表
     *
     * @param channelId
     * @return
     */
    @RequestMapping("channelArticles")
    @ResponseBody
    @CrossOrigin(origins = "*")
    public List<Channel> channelArticles(@RequestParam Long channelId) {
        return articleService.channelArticles(channelId);
    }

    /**
     * 根据栏目id查询文章列表
     *
     * @param elecBookLibQry
     * @return
     */
    @RequestMapping("elecBookLib")
    @ResponseBody
    @CrossOrigin(origins = "*")
    public List<Article> elecBookLib(ElecBookLibQry elecBookLibQry) {
        return articleService.elecBookLib(elecBookLibQry);
    }

    /**
     * 游客登录
     *
     * @return
     */
    @PostMapping("memberLogin")
    @ResponseBody
    @CrossOrigin(origins = "*")
    public R memberLogin(@RequestParam String username, @RequestParam String password, HttpServletRequest request) {
        return userService.memberLogin(username, password, request);
    }

    /**
     * 游客退出登录
     *
     * @return
     */
    @PostMapping("memberLogout")
    @ResponseBody
    @CrossOrigin(origins = "*")
    public R memberLogout(HttpServletRequest request) {
        HttpSession session = request.getSession();
        User user = LoginUserUtil.getLoginUser(request);
        session.removeAttribute(Constants.GUEST_USERNAME);
        session.invalidate();
        if (user != null) {
            LOGGER.info("游客退出登录:{}", user.getName());
        }
        return R.ok();
    }

    @GetMapping("currentLoginUser")
    @ResponseBody
    @CrossOrigin(origins = "*")
    public R currentLoginUser(HttpServletRequest request) {
        User user = LoginUserUtil.getLoginUser(request);
        if (null == user) {
            return R.failed("用户未登录");
        }
        user.setLastLoginTime(userLoginRecordService.getLastLoginTime(user.getId()));
        return R.ok(user);
    }

    @RequestMapping("encrypt")
    @ResponseBody
    @CrossOrigin(origins = "*")
    public R encrypt(String password) {
        return userService.encrypt(password);
    }

    /**
     * 将当前登录用户取出来并放回session中
     */
    @RequestMapping("setUser")
    @ResponseBody
    @CrossOrigin(origins = "*")
    public R setUser(HttpServletRequest request) {
        User loginUser = LoginUserUtil.getLoginUser(request);
        request.getSession().setAttribute(Constants.GUEST_USERNAME, loginUser);
        request.getSession().setAttribute(Constants.TARGET_USER, loginUser);
        userLoginRecordService.saveLoginRecord(loginUser.getId());
        return R.ok();
    }
}
