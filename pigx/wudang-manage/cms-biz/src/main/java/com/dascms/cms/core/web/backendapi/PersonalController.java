package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.component.PasswordService;
import com.dascms.cms.core.service.ConfigService;

import javax.validation.constraints.NotBlank;

/**
 * 个人设置 Controller
 *
 * <AUTHOR>
 */
//@RestController("backendPersonalController")
//@RequestMapping(BACKEND_API + "/core/personal")

public class PersonalController {
    private final ConfigService configService;
    private final PasswordService passwordService;
    public PersonalController(ConfigService configService, PasswordService passwordService) {
        this.configService = configService;
        this.passwordService = passwordService;
    }
/*    @PutMapping("password")
    //@PreAuthorize("hasAnyAuthority('password:update','*')")
    //@OperationLog(module = "personal", operation = "updatePassword", type = OperationType.UPDATE)
    public ResponseEntity<Responses.Body> updatePassword(
            @RequestBody UpdatePasswordParams params, HttpServletRequest request) {
        String ip = Servlets.getRemoteAddr(request);
        Config.Security security = configService.getUnique().getSecurity();
        User currentUser = Contexts.getCurrentUser();
        return passwordService.updatePassword(currentUser, params.password, params.newPassword, ip,
                security, request);
    }*/

    public static class UpdatePasswordParams {
        @NotBlank
        public String password;
        @NotBlank
        public String newPassword;
    }
}
