package com.dascms.cms.core.security;

import com.dascms.cms.core.service.ConfigService;
import com.dascms.cms.core.service.LoginLogService;
import com.dascms.cms.core.support.Props;
import com.dascms.commons.captcha.CaptchaTokenService;
import com.dascms.commons.captcha.IpLoginAttemptService;
import com.dascms.commons.security.AbstractLoginConfigurer;
import org.springframework.security.config.annotation.web.HttpSecurityBuilder;

/**
 * <AUTHOR>
 */
public class EncryptedPasswordLoginConfigurer<H extends HttpSecurityBuilder<H>>
        extends AbstractLoginConfigurer<H, EncryptedPasswordLoginConfigurer<H>, EncryptedPasswordAuthenticationFilter> {
    public EncryptedPasswordLoginConfigurer(
            Props props, ConfigService configService,
            LoginLogService loginLogService, CaptchaTokenService captchaTokenService,
            IpLoginAttemptService ipLoginAttemptService) {
        super(new EncryptedPasswordAuthenticationFilter(props, configService, loginLogService, captchaTokenService,
                ipLoginAttemptService), null);
    }

    @Override
    public EncryptedPasswordLoginConfigurer<H> loginPage(String loginPage) {
        return super.loginPage(loginPage);
    }
}
