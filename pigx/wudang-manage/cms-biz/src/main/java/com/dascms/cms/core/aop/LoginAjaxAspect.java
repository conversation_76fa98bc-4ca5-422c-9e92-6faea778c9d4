package com.dascms.cms.core.aop;

import com.alibaba.fastjson.JSON;
import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.support.LoginUserUtil;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * 过滤器不生效，加上展示端不是所有的功能都要登录，所以加了这个aop
 */
@Aspect
@Component
public class LoginAjaxAspect {

    @Pointcut("@annotation(com.dascms.cms.core.aop.annotations.RequiresAjaxLogin)")
    public void login() {
    }

    @Before("login()")
    public void checkLogin() throws IOException {
        // 获取当前请求和响应对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return; // 非Web环境，直接返回
        }
        HttpServletRequest request = attributes.getRequest();
        HttpServletResponse response = attributes.getResponse();

        // 检查用户是否已登录（假设你有一个方法isLoggedIn()来检查）
        boolean isLoggedIn = isLoggedIn(request);
        // 检查用户是否已登录
        if (!isLoggedIn(request)) {
            // 设置响应状态和内容类型
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType("application/json;charset=UTF-8");
            // 创建简单的响应对象，不要使用ResponseEntity
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("message", "please login");
            responseMap.put("status", HttpStatus.UNAUTHORIZED.value());
            // 使用JSON库序列化响应对象
            String jsonResponse = JSON.toJSONString(responseMap);
            // 写入响应
            try (PrintWriter writer = response.getWriter()) {
                writer.write(jsonResponse);
                writer.flush();
            }
            // 抛出异常阻止继续执行目标方法
            throw new RuntimeException("User not logged in");
        }
    }

    private boolean isLoggedIn(HttpServletRequest request) {
        User currentUser = LoginUserUtil.getLoginUser(request);
        if (currentUser == null) {
            return false;
        }
        return true;
    }
}