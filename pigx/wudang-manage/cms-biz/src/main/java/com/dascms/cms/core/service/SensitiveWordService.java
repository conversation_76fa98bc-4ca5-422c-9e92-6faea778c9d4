package com.dascms.cms.core.service;

import com.dascms.cms.core.domain.SensitiveWord;
import com.dascms.cms.core.domain.base.SensitiveWordBase;
import com.dascms.cms.core.mapper.SensitiveWordMapper;
import com.dascms.cms.core.service.args.SensitiveWordArgs;
import com.dascms.cms.core.web.request.SensitiveWordQry;
import com.dascms.commons.db.identifier.SnowflakeSequence;
import com.dascms.commons.query.QueryInfo;
import com.dascms.commons.query.QueryParser;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.pig4cloud.pigx.common.core.util.R;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 敏感词 Service
 *
 * <AUTHOR>
 */
@Service
public class SensitiveWordService {
    private final SensitiveWordMapper mapper;
    private final SnowflakeSequence snowflakeSequence;

    public SensitiveWordService(SensitiveWordMapper mapper, SnowflakeSequence snowflakeSequence) {
        this.mapper = mapper;
        this.snowflakeSequence = snowflakeSequence;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(SensitiveWord bean) {
        bean.setId(snowflakeSequence.nextId());
        mapper.insert(bean);
    }

    @Transactional(rollbackFor = Exception.class)
    public R update(SensitiveWord bean) {
        Integer exists = mapper.checkSame(bean.getName(), bean.getId());
        if (exists != null) {
            throw new RuntimeException("敏感词已存在");
        }
        return R.ok(mapper.update(bean));
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return mapper.delete(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(List<Long> ids) {
        return ids.stream().filter(Objects::nonNull).mapToInt(this::delete).sum();
    }

    @Nullable
    public SensitiveWord select(Long id) {
        return mapper.select(id);
    }

    public List<SensitiveWord> selectList(SensitiveWordArgs args) {
        QueryInfo queryInfo = QueryParser.parse(args.getQueryMap(), SensitiveWordBase.TABLE_NAME, "id_desc");
        return mapper.selectAll(queryInfo);
    }

    public List<SensitiveWord> selectList(SensitiveWordArgs args, int offset, int limit) {
        return PageMethod.offsetPage(offset, limit, false).doSelectPage(() -> selectList(args));
    }

    public Page<SensitiveWord> selectPage(SensitiveWordArgs args, int page, int pageSize) {
        return PageMethod.startPage(page, pageSize).doSelectPage(() -> selectList(args));
    }

    public Page<SensitiveWord> selectPage(SensitiveWordQry sensitiveWordQry) {
        return PageMethod.startPage(sensitiveWordQry.getPageNum(), sensitiveWordQry.getPageSize()).doSelectPage(() -> mapper.selectPage(sensitiveWordQry));
    }

    /**
     * 添加敏感词
     *
     * @param addSensitiveWord
     * @return
     */
    public R add(SensitiveWord addSensitiveWord) {
        Integer exists = mapper.checkSame(addSensitiveWord.getName(), null);
        if (exists != null) {
            throw new RuntimeException("敏感词已存在");
        }
        return R.ok(mapper.insert(addSensitiveWord));
    }
}