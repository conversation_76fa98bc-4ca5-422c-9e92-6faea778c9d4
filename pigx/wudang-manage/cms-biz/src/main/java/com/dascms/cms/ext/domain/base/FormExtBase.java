package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class FormExtBase extends Model<FormExtBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "form_ext";

    /**
     * 表单扩展ID
     */
    @NotNull
    @Schema(description="表单扩展ID")
    private Long id = 0L;

    /**
     * 自定义大字段
     */
    @Nullable
    @Schema(description="自定义大字段")
    private String clobsJson;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Nullable
    public String getClobsJson() {
        return clobsJson;
    }

    public void setClobsJson(@Nullable String clobsJson) {
        this.clobsJson = clobsJson;
    }
}