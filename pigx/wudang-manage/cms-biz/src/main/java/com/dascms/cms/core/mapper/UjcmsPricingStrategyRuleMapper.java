package com.dascms.cms.core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.material.UjcmsPricingStrategyRule;

/**
 * <p>
 * 定价方案规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface UjcmsPricingStrategyRuleMapper extends BaseMapper<UjcmsPricingStrategyRule> {

    /**
     * 根据定价方案id删除规则
     */
    void deleteByPricingStrategyId(@Param("strategyId") Integer strategyId);

    /**
     * 根据定价方案id查询规则
     */
    List<UjcmsPricingStrategyRule> selectRulesByPricingStrategyId(@Param("strategyId") Integer strategyId);

    /**
     * 根据定价方案id批量查询规则
     * 
     * @param pricingStrategyIds
     * @return
     * @param pricingStrategyIds
     * @return
     */
    List<UjcmsPricingStrategyRule> selectPricingStrategyRules(@Param("pricingStrategyIds")List<Integer> pricingStrategyIds);
}
