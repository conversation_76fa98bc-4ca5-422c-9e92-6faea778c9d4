package com.dascms.cms.core.web.directive.module.qry;

import com.pig4cloud.pigx.common.core.util.ListBuilder;
import freemarker.template.SimpleNumber;
import freemarker.template.TemplateModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 查询条件基类
 */
@Data
public class BaseQry {
    /**
     * 栏目id
     */
    protected List<Long> channelIds;

    /**
     * 排序字段
     *
     * @param params
     */
    protected String sortField;

    /**
     * 排序方式
     */
    protected String sortOrder;

    /**
     * 排序字段的表别名
     */
    protected String sortFieldTableAlias;

    /*
     *  如果不是分页的话，默认返回1000条数据
     */
    protected int defaultLimit = 1000;

    protected void initChannelIds(Map<String, TemplateModel> params) {
        SimpleNumber channelId = (SimpleNumber) params.get("channelId");
        channelIds = ListBuilder.<Long>ofList().build();
        if (channelId != null) {
            channelIds.add(Long.valueOf(channelId.toString()));
        }
    }

    public List<Long> getChannelIds() {
        return channelIds;
    }

    public void setChannelIds(List<Long> channelIds) {
        this.channelIds = channelIds;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getSortFieldTableAlias() {
        return sortFieldTableAlias;
    }

    public void setSortFieldTableAlias(String sortFieldTableAlias) {
        this.sortFieldTableAlias = sortFieldTableAlias;
    }

    public int getDefaultLimit() {
        return defaultLimit;
    }

    public void setDefaultLimit(int defaultLimit) {
        this.defaultLimit = defaultLimit;
    }
}
