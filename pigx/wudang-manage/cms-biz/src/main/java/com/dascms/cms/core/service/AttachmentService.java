package com.dascms.cms.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.dascms.cms.core.domain.Attachment;
import com.dascms.cms.core.domain.AttachmentRefer;
import com.dascms.cms.core.domain.base.AttachmentBase;
import com.dascms.cms.core.listener.SiteDeleteListener;
import com.dascms.cms.core.mapper.AttachmentMapper;
import com.dascms.cms.core.mapper.AttachmentReferMapper;
import com.dascms.cms.core.service.args.AttachmentArgs;
import com.dascms.commons.db.identifier.SnowflakeSequence;
import com.dascms.commons.file.FileHandler;
import com.dascms.commons.query.QueryInfo;
import com.dascms.commons.query.QueryParser;
import com.dascms.commons.web.PathResolver;
import com.dascms.commons.web.Uploads;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 附件 Service
 *
 * <AUTHOR>
 */
@Service
public class AttachmentService extends ServiceImpl<AttachmentMapper, Attachment> implements SiteDeleteListener, IService<Attachment> {

    private final AttachmentReferMapper referMapper;
    private final PathResolver pathResolver;
    private final SnowflakeSequence snowflakeSequence;


    public AttachmentService(@Lazy AttachmentReferMapper referMapper, PathResolver pathResolver,
                             SnowflakeSequence snowflakeSequence) {
        this.referMapper = referMapper;
        this.pathResolver = pathResolver;
        this.snowflakeSequence = snowflakeSequence;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertRefer(String referType, Long referId, List<String> urls) {
        Set<Long> ids = doInsertRefer(referType, referId, urls);
        if (!ids.isEmpty()) {
            this.baseMapper.updateUsed(ids);
        }
    }

    private Set<Long> doInsertRefer(String referType, Long referId, List<String> urls) {
        Set<Long> ids = new HashSet<>();
        urls.forEach(url -> {
            Attachment attachment = this.baseMapper.findByUrl(url);
            if (attachment != null && referMapper.select(attachment.getId(), referType, referId) == null) {
                Long id = snowflakeSequence.nextId();
                referMapper.insert(new AttachmentRefer(id, attachment.getId(), referType, referId));
                ids.add(attachment.getId());
            }
        });
        return ids;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRefer(String referType, Long referId, List<String> urls) {
        Set<Long> ids = findAttachmentIds(referType, referId);
        doDeleteRefer(referType, referId);
        if (!urls.isEmpty()) {
            ids.addAll(doInsertRefer(referType, referId, urls));
        }
        if (!ids.isEmpty()) {
            this.baseMapper.updateUsed(ids);
        }
    }

    private Set<Long> findAttachmentIds(String referType, Long referId) {
        Set<Long> ids = new HashSet<>();
        referMapper.listByReferTypeAndReferId(referType, referId).forEach(refer -> ids.add(refer.getAttachmentId()));
        return ids;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRefer(String referType, Long referId) {
        Set<Long> ids = findAttachmentIds(referType, referId);
        doDeleteRefer(referType, referId);
        if (!ids.isEmpty()) {
            this.baseMapper.updateUsed(ids);
        }
    }

    private void doDeleteRefer(String referType, Long referId) {
        referMapper.deleteByReferTypeAndReferId(referType, referId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(Attachment bean) {
        bean.setId(snowflakeSequence.nextId());
        this.baseMapper.insert(bean);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(Attachment bean) {
        this.baseMapper.update(bean);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        Attachment bean = select(id);
        if (bean != null) {
            return delete(bean);
        }
        return 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Attachment bean) {
        if (Boolean.FALSE.equals(bean.getUsed())) {
            FileHandler fileHandler = bean.getSite().getConfig().getUploadStorage().getFileHandler(pathResolver);
            Optional.ofNullable(fileHandler.getName(bean.getUrl())).ifPresent(pathname -> {
                fileHandler.deleteFileAndEmptyParentDir(pathname);
                // 删除缩略图，如果有的话
                fileHandler.deleteFileAndEmptyParentDir(Uploads.getThumbnailName(pathname));
            });
        }
        return this.baseMapper.delete(bean.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(List<Long> ids) {
        return ids.stream().filter(Objects::nonNull).mapToInt(this::delete).sum();
    }

    @Nullable
    public Attachment select(Long id) {
        return this.baseMapper.select(id);
    }

    public List<Attachment> selectList(AttachmentArgs args) {
        QueryInfo queryInfo = QueryParser.parse(args.getQueryMap(), AttachmentBase.TABLE_NAME, "id_desc");
        return this.baseMapper.selectAll(queryInfo);
    }

    public List<Attachment> selectList(AttachmentArgs args, int offset, int limit) {
        return PageMethod.offsetPage(offset, limit, false).doSelectPage(() -> selectList(args));
    }

    public Page<Attachment> selectPage(AttachmentArgs args, int page, int pageSize) {
        return PageMethod.startPage(page, pageSize).doSelectPage(() -> selectList(args));
    }

    /**
     * 统计附件数量
     *
     * @param siteId  站点ID
     * @param created 创建日期
     * @return 附件数量
     */
    public int countByCreated(Long siteId, @Nullable LocalDateTime created) {
        return this.baseMapper.countByCreated(siteId, created);
    }

    @Override
    public void preSiteDelete(Long siteId) {
        referMapper.deleteBySiteId(siteId);
        this.baseMapper.delete(siteId);
    }

    @Override
    public int deleteListenerOrder() {
        return 100;
    }
}