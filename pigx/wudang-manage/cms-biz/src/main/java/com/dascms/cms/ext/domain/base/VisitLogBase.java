package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pig4cloud.pigx.common.core.deserialize.DefaultLocalDateTimeDeserializer;
import com.pig4cloud.pigx.common.core.deserialize.DefaultLocalDateTimeSerializable;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class VisitLogBase extends Model<VisitLogBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "visit_log";

    /**
     * 访问日志ID
     */
    @NotNull
    @Schema(description = "访问日志ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @NotNull
    @Schema(description = "站点ID")
    private Long siteId = 0L;

    /**
     * 用户ID
     */
    @NotNull
    @Schema(description = "用户ID")
    private Long userId = 0L;

    /**
     * URL地址
     */
    @Length(max = 255)
    @NotNull
    @Schema(description = "URL地址")
    private String url = "";

    /**
     * 入口URL地址
     */
    @Length(max = 255)
    @NotNull
    @Schema(description = "入口URL地址")
    private String entryUrl = "";

    /**
     * 来源
     */
    @Length(max = 80)
    @NotNull
    @Schema(description = "来源")
    private String source = "";

    /**
     * 来源类型(DIRECT:直接访问,INNER:内部链接,OUTER:外部链接,SEARCH:搜索引擎)
     */
    @Length(max = 20)
    @NotNull
    @Schema(description = "来源类型(DIRECT:直接访问,INNER:内部链接,OUTER:外部链接,SEARCH:搜索引擎)")
    private String sourceType = "DIRECT";

    /**
     * 国家
     */
    @Length(max = 50)
    @NotNull
    @Schema(description = "国家")
    private String country = "";

    /**
     * 省份
     */
    @Length(max = 50)
    @NotNull
    @Schema(description = "省份")
    private String province = "";

    /**
     * 设备
     */
    @Length(max = 50)
    @NotNull
    @Schema(description = "设备")
    private String device = "";

    /**
     * 操作系统
     */
    @Length(max = 50)
    @NotNull
    @Schema(description = "操作系统")
    private String os = "";

    /**
     * 浏览器
     */
    @Length(max = 50)
    @NotNull
    @Schema(description = "浏览器")
    private String browser = "";

    /**
     * 用户代理
     */
    @Length(max = 255)
    @Nullable
    @Schema(description = "用户代理")
    private String userAgent;

    /**
     * 访问次数
     */
    @NotNull
    @Schema(description = "访问次数")
    private Integer count = 0;

    /**
     * 会话标识
     */
    @NotNull
    @Schema(description = "会话标识")
    private Long si = 0L;

    /**
     * 访客标识
     */
    @NotNull
    @Schema(description = "访客标识")
    private Long uv = 0L;

    /**
     * IP地址
     */
    @Length(max = 45)
    @NotNull
    @Schema(description = "IP地址")
    private String ip = "";

    /**
     * 访问日期
     */
    @NotNull
    @Schema(description = "访问日期")
    @JsonDeserialize(using = DefaultLocalDateTimeDeserializer.class)
    @JsonSerialize(using = DefaultLocalDateTimeSerializable.class)
    private LocalDateTime date = LocalDateTime.now();

    /**
     * 访问时长
     */
    @NotNull
    @Schema(description = "访问时长")
    private Integer duration = 0;

    /**
     * 是否新访客
     */
    @NotNull
    @Schema(description = "是否新访客")
    private Boolean newVisitor = false;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getEntryUrl() {
        return entryUrl;
    }

    public void setEntryUrl(String entryUrl) {
        this.entryUrl = entryUrl;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    @Nullable
    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(@Nullable String userAgent) {
        this.userAgent = userAgent;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Long getSi() {
        return si;
    }

    public void setSi(Long si) {
        this.si = si;
    }

    public Long getUv() {
        return uv;
    }

    public void setUv(Long uv) {
        this.uv = uv;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public LocalDateTime getDate() {
        return date;
    }

    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Boolean getNewVisitor() {
        return newVisitor;
    }

    public void setNewVisitor(Boolean newVisitor) {
        this.newVisitor = newVisitor;
    }
}