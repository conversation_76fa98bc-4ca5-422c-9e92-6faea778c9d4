package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class SurveyItemBase extends Model<SurveyItemBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "survey_item";

    /**
     * 问卷条目ID
     */
    @NotNull
    @Schema(description="问卷条目ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @NotNull
    @Schema(description="站点ID")
    private Long siteId = 0L;

    /**
     * 问卷ID
     */
    @NotNull
    @Schema(description="问卷ID")
    private Long surveyId = 0L;

    /**
     * 标题
     */
    @Length(max = 1000)
    @NotNull
    @Schema(description="标题")
    private String title = "";

    /**
     * 排序
     */
    @NotNull
    @Schema(description="排序")
    private Integer order = 999999;

    /**
     * 是否多选
     */
    @NotNull
    @Schema(description="是否多选")
    private Boolean multiple = false;

    /**
     * 是否问答
     */
    @NotNull
    @Schema(description="是否问答")
    private Boolean essay = false;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Boolean getMultiple() {
        return multiple;
    }

    public void setMultiple(Boolean multiple) {
        this.multiple = multiple;
    }

    public Boolean getEssay() {
        return essay;
    }

    public void setEssay(Boolean essay) {
        this.essay = essay;
    }
}