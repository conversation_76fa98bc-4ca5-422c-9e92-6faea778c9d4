package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.domain.Attachment;
import com.dascms.cms.core.service.AttachmentService;
import com.dascms.cms.core.service.args.AttachmentArgs;
import com.dascms.cms.core.support.Contexts;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.cms.core.web.support.ValidUtils;
import com.dascms.commons.web.Responses;
import com.dascms.commons.web.Responses.Body;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.dascms.cms.core.support.Constants.validPage;
import static com.dascms.cms.core.support.Constants.validPageSize;
import static com.dascms.commons.db.MyBatis.springPage;
import static com.dascms.commons.query.QueryUtils.getQueryMap;

/**
 * 附件 Controller
 *
 * <AUTHOR>
 */
@RestController("backendAttachmentController")
@RequestMapping(UrlConstants.BACKEND_API + "/core/attachment")
public class AttachmentController {
    private final AttachmentService service;

    public AttachmentController(AttachmentService service) {
        this.service = service;
    }

    @GetMapping
    //@PreAuthorize("hasAnyAuthority('attachment:list','*')")
    public Object list(Integer page, Integer pageSize, HttpServletRequest request) {
        AttachmentArgs args = AttachmentArgs.of(getQueryMap(request.getQueryString()))
                .siteId(Contexts.getCurrentSiteId());
        return springPage(service.selectPage(args, validPage(page), validPageSize(pageSize)));
    }

    @GetMapping("{id}")
    //@PreAuthorize("hasAnyAuthority('attachment:show','*')")
    public Object show(@PathVariable Long id) {
        Attachment bean = service.select(id);
        if (bean == null) {
            return Responses.notFound("Attachment not found. ID = " + id);
        }
        ValidUtils.dataInSite(bean.getSiteId(), Contexts.getCurrentSiteId());
        return bean;
    }

    @DeleteMapping
    //@PreAuthorize("hasAnyAuthority('attachment:delete','*')")
    //@OperationLog(module = "attachment", operation = "delete", type = OperationType.DELETE)
    public ResponseEntity<Body> delete(@RequestBody List<Long> ids) {
        Long siteId = Contexts.getCurrentSiteId();
        for (Long id : ids) {
            Attachment bean = service.select(id);
            if (bean == null) {
                continue;
            }
            ValidUtils.dataInSite(bean.getSiteId(), siteId);
            service.delete(bean);
        }
        return Responses.ok();
    }
}