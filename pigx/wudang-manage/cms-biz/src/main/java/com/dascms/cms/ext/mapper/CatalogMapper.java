package com.dascms.cms.ext.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.catalog.Catalog;
import com.dascms.cms.core.service.args.CatalogArgs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 编目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
public interface CatalogMapper extends BaseMapper<Catalog> {

    /**
     * 根据名称和父级编号查询
     *
     * @param catalogName
     * @param catalogParentId
     * @return
     */
    Integer getByNameAndParentNo(@Param("catalogName") String catalogName, @Param("catalogParentId") Integer catalogParentId);

    /**
     * 查询类目列表
     *
     * @param args
     * @return
     */
    List<Catalog> list(CatalogArgs args);

    /**
     * 根据父级编号查询子级列表
     *
     * @param parentNos
     * @param catalogState
     * @return
     */
    List<Catalog> listByParentNo(@Param("parentNos") List<Integer> parentNos, @Param("catalogState") Integer catalogState);

    /**
     * 查询没有子级的类目数量
     *
     * @return
     */
    Integer countNoChildCatalog();
}
