package com.dascms.cms.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.material.UjcmsMaterialPrice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
public interface UjcmsMaterialPriceMapper extends BaseMapper<UjcmsMaterialPrice> {
    /**
     * 根据素材id删除可选规格
     *
     * @param materialId
     */
    void deleteByMaterialId(@Param("materialId") Long materialId);

    /**
     * 根据素材id查询可选规格
     *
     * @param pricingStrategyIds
     * @return
     */
    List<UjcmsMaterialPrice> selectPrices(@Param("pricingStrategyIds") List<Long> pricingStrategyIds);

    /**
     * 根据素材id和规格查询可选规格
     *
     * @param materialId
     * @param norm
     * @return
     */
    UjcmsMaterialPrice getByMaterialIdAndNorm(@Param("materialId") Long materialId, @Param("norm") String norm);

    /**
     * 根据md5查询素材id
     * @param md5
     * @return
     */
    Long getByMd5(String md5);
}
