package com.dascms.cms.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.user.UserLoginRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface UserLoginRecordMapper extends BaseMapper<UserLoginRecord> {
    /**
     * 根据用户id查询登录记录
     * @param userId
     * @return
     */
    List<UserLoginRecord> getByUserId(@Param("userId") Long userId);
}
