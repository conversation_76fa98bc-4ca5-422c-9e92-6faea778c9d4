package com.dascms.commons.freemarker;

import com.dascms.cms.CmsBackendlication;
import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.service.SiteService;
import com.dascms.cms.core.web.support.Directives;
import com.dascms.commons.web.PageUrlResolver;
import freemarker.core.Environment;
import freemarker.template.TemplateMethodModelEx;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Pattern;

/**
 * FreeMarker翻页方法
 *
 * <ul>
 * <li>普通的动态地址：      /channel/news             -> /channel/news?page=2
 * <li>带查询条件的动态地址：/channel/news?type=comedy -> /channel/news/?type=comedy&page=3
 * <li>静态页目录地址：      /news/                    -> /news/index_2.html
 * <li>静态页目录地址：      /news/index_2.html        -> /news/index_3.html
 * </ul>
 *
 * <AUTHOR>
 */
public class PagingMethod implements TemplateMethodModelEx {
    @Override
    public Object exec(List args) throws TemplateModelException {
        Integer page = null;
        if (args.size() > 0) {
            TemplateModel arg0 = (TemplateModel) args.get(0);
            page = Freemarkers.getInteger(arg0);
        }
        Environment env = Environment.getCurrentEnvironment();
        String queryString = StringUtils.trim(Directives.getQueryString(env));
        if (StringUtils.isNotBlank(queryString)) {
            // 删除原有page。page=3&page=4&page=10&page=0
            queryString = pattern.matcher(queryString).replaceAll("");
        }
        PageUrlResolver pageUrlResolver = Directives.getPageUrlResolver(env);
        if (pageUrlResolver != null) {
            if (StringUtils.isNotBlank(queryString)) {
                String dynamicUrl = pageUrlResolver.getDynamicUrl(page != null ? page : 1);
                if (dynamicUrl.contains("?")) {
                    if (queryString.contains("&")) {
                        return dynamicUrl + queryString;
                    }
                    return dynamicUrl + "&" + queryString;
                }
                return dynamicUrl + "?" + queryString;
            }
            return pageUrlResolver.getUrl(page != null ? page : 1);
        }
        SiteService articleService = CmsBackendlication.applicationContext.getBean(SiteService.class);
        Site site = articleService.select(1l);
        String port = site.getConfig().getPort() == null ? "" : site.getConfig().getPort().toString();
        String url = site.getProtocol() + "://" + site.getDomain() + ":" + port + site.getConfig().getContextPath() + "/" + site.getSubDir() + "/search";
        if (page == null || page <= 1) {
            if (StringUtils.isNotBlank(queryString)) {
                return url + "?" + queryString;
            }
            return url;
        }
        if (StringUtils.isNotBlank(queryString)) {
            return url + "?" + queryString + "&" + param + "=" + page;
        }
        return url + "?" + param + "=" + page;
    }

    private String param;
    private Pattern pattern;

    public PagingMethod(String param) {
        this.param = param;
        this.pattern = Pattern.compile("&*\\s*" + param + "\\s*=[^&]*");
    }
}
