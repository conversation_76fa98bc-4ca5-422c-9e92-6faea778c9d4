package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class SurveyOptionFeedbackBase extends Model<SurveyOptionFeedbackBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "survey_option_feedback";

    /**
     * 主键ID
     */
    @NotNull
    @Schema(description="主键ID")
    private Long id = 0L;

    /**
     * 问卷选项ID
     */
    @NotNull
    @Schema(description="问卷选项ID")
    private Long surveyOptionId = 0L;

    /**
     * 问卷反馈ID
     */
    @NotNull
    @Schema(description="问卷反馈ID")
    private Long surveyFeedbackId = 0L;

    /**
     * 问卷ID
     */
    @NotNull
    @Schema(description="问卷ID")
    private Long surveyId = 0L;

    /**
     * 问卷条目ID
     */
    @NotNull
    @Schema(description="问卷条目ID")
    private Long surveyItemId = 0L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSurveyOptionId() {
        return surveyOptionId;
    }

    public void setSurveyOptionId(Long surveyOptionId) {
        this.surveyOptionId = surveyOptionId;
    }

    public Long getSurveyFeedbackId() {
        return surveyFeedbackId;
    }

    public void setSurveyFeedbackId(Long surveyFeedbackId) {
        this.surveyFeedbackId = surveyFeedbackId;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public Long getSurveyItemId() {
        return surveyItemId;
    }

    public void setSurveyItemId(Long surveyItemId) {
        this.surveyItemId = surveyItemId;
    }
}