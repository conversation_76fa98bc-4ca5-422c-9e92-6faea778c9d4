package com.dascms.cms.core.mapper;

import com.dascms.cms.core.domain.ArticleRelicAttach;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 文物附件
 */
@Mapper
@Repository
public interface ArticleRelicAttachMapper {
    /**
     * 根据文章id查询附件
     */
    List<ArticleRelicAttach> selectByArticleId(@Param("articleId") Long articleId);

    /**
     * 批量保存附件
     *
     * @param articleRelicAttaches
     */
    void batchSaveAttaches(@Param("articleRelicAttaches") List<ArticleRelicAttach> articleRelicAttaches);

    /**
     * 删除原来的附件数据
     */
    void deleteByArticleId(@Param("articleId") Long articleId);
}
