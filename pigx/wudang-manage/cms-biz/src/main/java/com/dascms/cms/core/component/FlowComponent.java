package com.dascms.cms.core.component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import com.pig4cloud.pigx.admin.api.entity.SysUser;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.flow.task.api.feign.RemoteFlowEngineService;
import com.pig4cloud.pigx.flow.task.dto.ProcessInstanceDeleteDto;
import com.pig4cloud.pigx.flow.task.dto.ProcessInstanceParamDto;
import com.dascms.cms.core.domain.Article;
import com.dascms.cms.core.web.directive.AnchorDirective;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class FlowComponent {
    private static final Logger log = LoggerFactory.getLogger(AnchorDirective.class);

    private final RemoteFlowEngineService remoteFlowEngineService;

    public FlowComponent(RemoteFlowEngineService remoteFlowEngineService) {
        this.remoteFlowEngineService = remoteFlowEngineService;
    }

    /**
     * 启动文章审核流程
     *
     * @param article
     * @param processKey
     * @param user
     */

    public void startArticleProcess(Article article, String processKey, SysUser user) {
        Long userId = user.getUserId();
        ProcessInstanceParamDto dto = new ProcessInstanceParamDto();
        dto.setStartUserId(userId.toString());
        dto.setFlowId(processKey);
        dto.setBusinessKey(article.getId().toString());

        Map<String, Object> paramMap = new HashMap<>();
        Dict rootUser = Dict.create().set("id", userId).set("name", user.getUsername()).set("type", "user");
        paramMap.put("root", CollUtil.newArrayList(rootUser));

        dto.setParamMap(paramMap);

        R<String> instanceR = remoteFlowEngineService.startProcess(dto);

        if (!instanceR.isOk()) {
            log.error("cms调用启动流程失败： 流程定义key: {}    发起人:{}", processKey, userId);
            throw new RuntimeException(instanceR.getMsg());
        }
        String processInstanceId = instanceR.getData();
        article.setStatus(Article.STATUS_PENDING);
        article.setProcessInstanceId(processInstanceId);
    }

    /**
     * 获取流程是否结束
     *
     * @param processInstanceId
     * @return
     */
    public boolean ended(String processInstanceId) {

        R<Boolean> ended = remoteFlowEngineService.ended(processInstanceId);

        if (!ended.isOk()) {
            log.error("cms调用获取流程是否结束失败： 流程实例id: {}     ", processInstanceId);
            throw new RuntimeException(ended.getMsg());
        }

        return ended.getData();
    }

    /**
     * 删除流程实例
     *
     * @param processInstanceId
     * @param username
     */
    public void deleteProcessInstance(String processInstanceId, String username) {
        ProcessInstanceDeleteDto dto = new ProcessInstanceDeleteDto();
        dto.setUsername(username);
        dto.setProcessInstanceId(processInstanceId);
        remoteFlowEngineService.deleteProcessInstance(dto);
    }


    public R<Map<String, Map<String, String>>> todoList(String userId, List<String> processInstanceIdList) {
        return remoteFlowEngineService.todoList(userId, null, processInstanceIdList, null, null);
    }
}
