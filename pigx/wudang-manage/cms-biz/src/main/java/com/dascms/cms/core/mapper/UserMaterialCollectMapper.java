package com.dascms.cms.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.material.UserMaterialCollect;
import com.dascms.cms.core.domain.order.Order;
import com.dascms.cms.core.service.args.OrderArgs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface UserMaterialCollectMapper extends BaseMapper<UserMaterialCollect> {

    /**
     * 获取用户收藏的素材
     *
     * @param materialId
     * @param userId
     * @return
     */
    UserMaterialCollect get(@Param("materialId") Long materialId, @Param("userId") Long userId);

    /**
     * 查询用户收藏的素材
     *
     * @param userId
     * @return
     */
    List<Long> getByUserId(@Param("userId") Long userId);

    /**
     * 统计用户收藏的素材数量
     *
     * @param id
     * @return
     */
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 我收藏的订单列表
     *
     * @param args
     * @return
     */
    List<Order> list(OrderArgs args);

    /**
     * 统计用户收藏的素材中不同类别的数量
     *
     * @param id
     * @param i
     * @return
     */
    Long countByUserIdAndType(@Param("userId") Long userId, @Param("type") int type);

    /**
     * 删除用户收藏的素材
     *
     */
    void deleteByMaterialId(@Param("materialId") Long materialId);
}
