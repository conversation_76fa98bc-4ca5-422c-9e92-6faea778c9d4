package com.dascms.cms.core.web.directive;

import com.dascms.cms.core.domain.Tag;
import com.dascms.cms.core.domain.count.ChannelCount;
import com.dascms.cms.core.service.TagService;
import com.dascms.cms.core.service.args.TagArgs;
import com.dascms.cms.core.support.Frontends;
import com.dascms.cms.core.web.support.Directives;
import com.dascms.commons.freemarker.Freemarkers;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import freemarker.core.Environment;
import freemarker.template.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.dascms.cms.core.web.support.Directives.getBoolean;
import static com.dascms.cms.core.web.support.Directives.getLong;
import static com.dascms.commons.db.MyBatis.springPage;

/**
 * TAG列表 标签
 *
 * <AUTHOR>
 */
public class TagListDirective implements TemplateDirectiveModel {
    /**
     * 站点ID。整型(Integer)。
     */
    public static final String SITE_ID = "siteId";
    /**
     * 名称。字符串(String)。
     */
    public static final String NAME = "name";
    /**
     * 是否被引用。布尔型(Boolean)。可选值：all(全部), false(禁用)。默认值：启用。
     */
    public static final String IS_REFERRED = "isReferred";
    /**
     * 是否获取所有站点的Tag。布尔型(Boolean)。默认：false。
     */
    public static final String IS_ALL_SITE = "isAllSite";

    public static void assemble(TagArgs args, Map<String, ?> params, Long defaultSiteId) {
        Long siteId = getLong(params, SITE_ID);
        // 不获取所有站点，则给默认站点ID
        if (siteId == null && !getBoolean(params, IS_ALL_SITE, false)) {
            siteId = defaultSiteId;
        }
        args.siteId(siteId);
        Optional.ofNullable(Directives.getBooleanDefault(params, IS_REFERRED, true)).ifPresent(args::isReferred);
        Optional.ofNullable(Directives.getString(params, NAME)).ifPresent(args::name);
        Directives.handleOrderBy(args.getQueryMap(), params, "refers_desc,id_desc");
    }

    protected void doExecute(Environment env, Map<String, TemplateModel> params, TemplateModel[] loopVars,
                             TemplateDirectiveBody body, boolean isPage) throws TemplateException, IOException {
        Freemarkers.requireLoopVars(loopVars);
        Freemarkers.requireBody(body);

        TagArgs args = TagArgs.of(Directives.getQueryMap(params));
        assemble(args, params, Frontends.getSiteId(env));

        SimpleNumber channelId = (SimpleNumber) params.get("channelId");
        List<Long> channelIds = ListBuilder.<Long>ofList().build();
        if (channelId != null) {
            channelIds.add(Long.valueOf(channelId.toString()));
        }
        if (isPage) {
            int page = Directives.getPage(params, env);
            int pageSize = Directives.getPageSize(params, env);
            Page<Tag> pagedList = springPage(service.selectPage(args, page, pageSize));
            if (CollectionUtils.isNotEmpty(channelIds)) {
                tagArticleCount(pagedList.getContent(), channelIds);
            }
            Directives.setTotalPages(pagedList.getTotalPages());
            loopVars[0] = env.getObjectWrapper().wrap(pagedList);
        } else {
            int offset = Directives.getOffset(params);
            int limit = Directives.getLimit(params);
            List<Tag> list = service.selectList(args, offset, limit);
            if (CollectionUtils.isNotEmpty(channelIds)) {
                tagArticleCount(list, channelIds);
            }
            loopVars[0] = env.getObjectWrapper().wrap(list);
        }
        body.render(env.getOut());
    }

    /**
     * 统计tag下面的文章数量
     *
     * @throws TemplateException
     * @throws IOException
     */
    private void tagArticleCount(List<Tag> tags, List<Long> channelIds) {
        if (CollectionUtils.isNotEmpty(tags)) {
            List<Long> tagIds = tags.parallelStream().map(Tag::getId).collect(Collectors.toList());
            List<ChannelCount> channelCounts = service.tagArticleCount(tagIds, channelIds);
            if (CollectionUtils.isNotEmpty(channelCounts)) {
                Map<Long, Integer> countMap = channelCounts.parallelStream().collect(Collectors.toMap(ChannelCount::getChannelId, ChannelCount::getCount));
                for (Tag tag : tags) {
                    tag.setArticleCount(countMap.get(tag.getId()));
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void execute(Environment env, Map params, TemplateModel[] loopVars, TemplateDirectiveBody body)
            throws TemplateException, IOException {
        doExecute(env, params, loopVars, body, false);
    }

    private final TagService service;

    public TagListDirective(TagService service) {
        this.service = service;
    }
}
