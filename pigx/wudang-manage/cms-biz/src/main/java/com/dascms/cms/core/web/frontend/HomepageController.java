package com.dascms.cms.core.web.frontend;

import cn.hutool.http.HttpUtil;
import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.service.SiteService;
import com.dascms.cms.core.support.Frontends;
import com.dascms.cms.core.web.support.SiteResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 前台首页 Controller
 *
 * <AUTHOR>
 */
@Controller("frontendHomepageController")
public class HomepageController {
    private final SiteResolver siteResolver;

    @Autowired
    private SiteService siteService;

    public HomepageController(SiteResolver siteResolver) {
        this.siteResolver = siteResolver;
    }

    @GetMapping({"/", "/{subDir:[\\w-]+}"})
    public String home(@PathVariable(required = false) String subDir,
                       HttpServletRequest request, Map<String, Object> modelMap) {
        Site site = siteResolver.resolve(request, subDir);
        modelMap.put(Frontends.PAGE_SIZE, site.getPageSize());
        modelMap.put("isHome", true);
        String template = site.getTemplate();
        System.out.println(template);
        return template;
    }

    @ResponseBody
    @GetMapping("/metrics")
    public String getMetrics() {
        Site site = siteService.select(1l);
        String port = site.getConfig().getPort() == null ? "" : site.getConfig().getPort().toString();
        String loginUrl = site.getProtocol() + "://" + site.getDomain() + ":" + port + site.getConfig().getContextPath() + "/actuator/prometheus";
        String s = HttpUtil.get(loginUrl);
        if (!s.endsWith("# EOF")) {
            s += "# EOF";
        }
        return s; // 返回 JSON 响应
    }

}
