package com.dascms.cms.core.support;

import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.domain.UserExt;
import com.dascms.cms.core.service.SiteService;
import com.pig4cloud.pigx.admin.api.entity.SysUser;
import com.pig4cloud.pigx.admin.api.feign.RemoteUserService;
import com.pig4cloud.pigx.common.core.constant.SecurityConstants;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.core.util.SpringContextHolder;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import org.slf4j.Logger;

import javax.servlet.http.HttpServletRequest;

public class LoginUserUtil {
    private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(LoginUserUtil.class);

    /**
     * 获取当前登录用户
     */
    public static User getLoginUser(HttpServletRequest request) {
        User currentUser = (User) request.getSession().getAttribute(Constants.GUEST_USERNAME);
        SysUser sysUser = null;
        RemoteUserService remoteUserService = SpringContextHolder.getBean(RemoteUserService.class);
        if (currentUser == null) {
            PigxUser user = SecurityUtils.getUser();
            if (user != null) {
                //重新查询，防止缓存
                R<SysUser> userById = remoteUserService.getUserById(user.getId(), SecurityConstants.FROM_IN);
                sysUser = userById.getData();
                UserExt userExt = new UserExt();
                currentUser = new User();
                currentUser.setExt(userExt);
            }
        } else {
            R<SysUser> userById = remoteUserService.getUserById(currentUser.getId(), SecurityConstants.FROM_IN);
            sysUser = userById.getData();
        }
        if (sysUser != null) {
            convertUser(sysUser, currentUser);
        }
        return currentUser;
    }

    public static void convertUser(com.pig4cloud.pigx.admin.api.entity.SysUser user, User result) {
        result.setId(user.getUserId());
        result.setUsername(user.getUsername());
        result.setNickname(user.getNickname());
        result.setAvatar(user.getAvatar());
        result.setEmail(user.getEmail());
        result.setMobile(user.getPhone());
        result.setGender(user.getGender() == null ? 0 : user.getGender());
        result.setStatus(user.getStatus());
        result.setRegistTime(user.getRegistTime());
        result.setRemark(user.getRemark());
        result.setWetchat(user.getWetchat());
        result.setProvince(user.getProvince());
        result.setCity(user.getCity());
        result.setContact(user.getContact());
        result.setTelephone(user.getTelephone());
        result.setAddress(user.getAddress());
        result.setLocation(user.getLocation());
        result.setBirthday(user.getBirthday());
        result.setWetchat(user.getWetchat());
        result.setProvince(user.getProvince());
        result.setCity(user.getCity());
        result.setContact(user.getContact());
        result.setTelephone(user.getTelephone());
        result.setAddress(user.getAddress());
        result.setVerificationIdStatus(user.getVerificationIdStatus());
        result.setRegistTime(user.getRegistTime());
        result.setIdCard(user.getIdCard());
        result.setEnterprise(user.getEnterprise());
        result.setContact(user.getContact());
        result.setTelephone(user.getTelephone());
        result.setAddress(user.getAddress());
        result.setRemark(user.getRemark());
    }

    /**
     * 获取登录页面
     */
    public static String getLoginPage() {
        SiteService siteService = SpringContextHolder.getBean(SiteService.class);
        Site site = siteService.select(1l);
        Integer port = site.getConfig().getPort();
        String loginUrl = "http://" + site.getDomain() + ":" + (port == null ? "" : port) + "" + site.getConfig().getContextPath() + "/" + site.getSubDir() + "/login";
        LOGGER.info("redirect_login:{}", loginUrl);
        return loginUrl;
    }

    public static com.dascms.cms.core.domain.User convertUser(com.pig4cloud.pigx.admin.api.entity.SysUser user) {
        com.dascms.cms.core.domain.User result = new com.dascms.cms.core.domain.User();
        convertUser(user, result);
        return result;
    }
}
