package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class VoteOptionBase extends Model<VoteOptionBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "vote_option";

    /**
     * 投票选项ID
     */
    @NotNull
    @Schema(description="投票选项ID")
    private Long id = 0L;

    /**
     * 投票ID
     */
    @NotNull
    @Schema(description="投票ID")
    private Long voteId = 0L;

    /**
     * 标题
     */
    @Length(max = 300)
    @NotNull
    @Schema(description="标题")
    private String title = "";

    /**
     * 得票数
     */
    @NotNull
    @Schema(description="得票数")
    private Integer count = 0;

    /**
     * 排列顺序
     */
    @NotNull
    @Schema(description="排列顺序")
    private Integer order = 999999;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVoteId() {
        return voteId;
    }

    public void setVoteId(Long voteId) {
        this.voteId = voteId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }
}