package com.dascms.cms.core;

import com.dascms.cms.core.support.Props;
import com.dascms.commons.db.CharBooleanTypeHandler;
import com.dascms.commons.db.DataScriptInitializer;
import com.dascms.commons.db.JsonStringTypeHandler;
import com.dascms.commons.db.identifier.SnowflakeSequence;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.ResourceLoader;

import javax.sql.DataSource;

import static com.dascms.commons.db.identifier.SnowflakeSequence.MAX_DATACENTER_ID;
import static com.dascms.commons.db.identifier.SnowflakeSequence.MAX_WORKER_ID;

/**
 * MyBatis 配置
 *
 * <AUTHOR>
 */
@Configuration
public class MyBatisConfig {
    /**
     * 雪花算法ID生成器
     */
    @Bean
    public SnowflakeSequence snowflakeSequence(Props props) {
        int datacenterId = props.getDatacenterId();
        int workerId = props.getWorkerId();
        if (datacenterId >= 0 && datacenterId <= MAX_DATACENTER_ID && workerId >= 0 && workerId <= MAX_WORKER_ID) {
            return new SnowflakeSequence(datacenterId, workerId);
        }
        return new SnowflakeSequence(null);
    }

    /**
     * 数值型 boolean 类型处理
     */
    @Bean
    public TypeHandler<Boolean> charBooleanTypeHandler() {
        return new CharBooleanTypeHandler();
    }

    /**
     * JSON 转 String 类型处理
     */
    @Bean
    public ConfigurationCustomizer mybatisConfigurationCustomizer() {
        return configuration -> configuration.getTypeHandlerRegistry()
                .register(String.class, JdbcType.OTHER, new JsonStringTypeHandler(configuration));
    }


    /**
     * 数据初始化
     */
    @Bean
    @DependsOn("liquibase")
    @ConditionalOnProperty(prefix = "ujcms", name = "data-sql-enabled", matchIfMissing = true)
    public DataScriptInitializer databaseInitializer(
            Props props, DataSource dataSource, ResourceLoader resourceLoader) {
        return new DataScriptInitializer(dataSource, resourceLoader, "ujcms_config", props.getDataSqlPlatform());
    }
}
