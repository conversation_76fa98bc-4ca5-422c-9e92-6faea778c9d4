package com.dascms.cms.core.pool;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfig {
    @Bean("asyncTaskExecutor")
    public ThreadPoolTaskExecutor getAsyncExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(5); // 核心线程数
        threadPoolTaskExecutor.setMaxPoolSize(10); // 最大线程数
        threadPoolTaskExecutor.setQueueCapacity(500); // 队列容量
        threadPoolTaskExecutor.setThreadNamePrefix("cms"); // 线程前缀
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }
}