package com.dascms.cms.core.web.directive.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.dascms.cms.core.domain.Article;
import com.dascms.cms.core.mapper.ArticleMapper;
import com.dascms.cms.core.web.directive.module.qry.ResearchResourcesQry;
import com.dascms.cms.core.web.directive.module.service.ModuleService;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import freemarker.template.TemplateModel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.dascms.cms.core.support.Constants.EXT_ALIAS;
import static com.dascms.cms.core.support.Constants.MAIN_ALIAS;
import static com.dascms.commons.query.QueryUtils.UNDERLINE;

@Service("researchResources")
public class ResearchResourcesService implements ModuleService<Article> {

    //主表排序字段
    private static final List<String> MAIN_SORT_Fields = ListBuilder.<String>ofList().add("created").build();

    private static final Logger LOGGER = LoggerFactory.getLogger(ResearchResourcesService.class);

    @Autowired
    private ArticleMapper articleMapper;

    private ResearchResourcesQry initQry(Map<String, TemplateModel> params) {
        LOGGER.info("query_params:{}", JSON.toJSONString(params));
        ResearchResourcesQry researchResourcesQry = new ResearchResourcesQry(params);
        TemplateModel tag = params.get("tag");
        if (tag != null && StringUtils.isNotBlank(tag.toString())) {
            String[] split = tag.toString().split(",");
            List<Long> tagIds = ListBuilder.<Long>ofList().build();
            for (String s : split) {
                tagIds.add(Long.parseLong(s));
            }
            researchResourcesQry.setTagIds(tagIds);
        }
        TemplateModel language = params.get("language");
        if (language != null && StringUtils.isNotBlank(language.toString())) {
            String[] split = language.toString().split(",");
            List<String> languages = ListBuilder.<String>ofList().build();
            for (String s : split) {
                languages.add(s);
            }
            researchResourcesQry.setLanguages(languages);
        }
        TemplateModel title = params.get("q");
        if (title != null) {
            researchResourcesQry.setTitle(title.toString());
        }
        TemplateModel orderBy = params.get("orderBy");
        if (orderBy != null) {
            String order = orderBy.toString();
            String[] split = order.split(UNDERLINE);
            researchResourcesQry.setSortField(split[0] + UNDERLINE);
            researchResourcesQry.setSortOrder(split[1]);
            if (MAIN_SORT_Fields.contains(split[0])) {
                researchResourcesQry.setSortFieldTableAlias(MAIN_ALIAS);
            } else {
                researchResourcesQry.setSortFieldTableAlias(EXT_ALIAS);
            }
        }
        return researchResourcesQry;
    }

    public Page<Article> selectPage(Map<String, TemplateModel> params, int page, int pageSize) {
        return PageMethod.startPage(page, pageSize).doSelectPage(() -> articleMapper.selectPage(initQry(params)));
    }

    @Override
    public List<Article> selectList(Map<String, TemplateModel> params, int offset, int limit) {
        Page<Article> articles = PageMethod.startPage(offset, limit).doSelectPage(() -> articleMapper.selectPage(initQry(params)));
        return articles.getResult();
    }
}
