package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pig4cloud.pigx.common.core.deserialize.DefaultLocalDateTimeDeserializer;
import com.pig4cloud.pigx.common.core.deserialize.DefaultLocalDateTimeSerializable;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class VoteBase extends Model<VoteBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "vote";

    /**
     * 投票ID
     */
    @NotNull
    @Schema(description="投票ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @NotNull
    @Schema(description="站点ID")
    private Long siteId = 0L;

    /**
     * 标题
     */
    @Length(max = 1000)
    @NotNull
    @Schema(description="标题")
    private String title = "";

    /**
     * 描述
     */
    @Length(max = 1000)
    @Nullable
    @Schema(description="描述")
    private String description;

    /**
     * 开始日期
     */
    @Nullable
    @Schema(description="开始日期")
    @JsonDeserialize(using = DefaultLocalDateTimeDeserializer.class)
    @JsonSerialize(using = DefaultLocalDateTimeSerializable.class)
    private LocalDateTime beginDate;

    /**
     * 结束日期
     */
    @Nullable
    @Schema(description="结束日期")
    @JsonDeserialize(using = DefaultLocalDateTimeDeserializer.class)
    @JsonSerialize(using = DefaultLocalDateTimeSerializable.class)
    private LocalDateTime endDate;

    /**
     * 创建日期
     */
    @NotNull
    @Schema(description="创建日期")
    @JsonDeserialize(using = DefaultLocalDateTimeDeserializer.class)
    @JsonSerialize(using = DefaultLocalDateTimeSerializable.class)
    private LocalDateTime created = LocalDateTime.now();

    /**
     * 重复投票间隔天数(0:不可重复投票)
     */
    @NotNull
    @Schema(description="重复投票间隔天数(0:不可重复投票)")
    private Integer interval = 0;

    /**
     * 参与人次
     */
    @NotNull
    @Schema(description="参与人次")
    private Integer times = 0;

    /**
     * 模式(1:独立访客,2:独立IP,3:独立用户)
     */
    @NotNull
    @Schema(description="模式(1:独立访客,2:独立IP,3:独立用户)")
    private Short mode = 1;

    /**
     * 是否多选
     */
    @NotNull
    @Schema(description="是否多选")
    private Boolean multiple = false;

    /**
     * 是否启用
     */
    @NotNull
    @Schema(description="是否启用")
    private Boolean enabled = true;

    /**
     * 排列顺序
     */
    @NotNull
    @Schema(description="排列顺序")
    private Long order = 0L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Nullable
    public String getDescription() {
        return description;
    }

    public void setDescription(@Nullable String description) {
        this.description = description;
    }

    @Nullable
    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(@Nullable LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    @Nullable
    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(@Nullable LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public LocalDateTime getCreated() {
        return created;
    }

    public void setCreated(LocalDateTime created) {
        this.created = created;
    }

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public Short getMode() {
        return mode;
    }

    public void setMode(Short mode) {
        this.mode = mode;
    }

    public Boolean getMultiple() {
        return multiple;
    }

    public void setMultiple(Boolean multiple) {
        this.multiple = multiple;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }
}