package com.dascms.cms.core.support;


import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单号生成工具类 - 高并发分布式环境适用
 */
public class OrderNumberGenerator {

    // 默认业务前缀
    private static final String DEFAULT_PREFIX = "ORD";

    // 原子计数器，用于保证线程安全
    private static final AtomicInteger SEQUENCE = new AtomicInteger(1);

    // 序列号长度
    private static final int SEQUENCE_LENGTH = 6;

    // 随机数长度
    private static final int RANDOM_LENGTH = 6;

    // 日期格式化器 - 精确到毫秒
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    // 记录上次生成订单号的时间戳
    private static long lastTimestamp = System.currentTimeMillis();

    // 工作机器ID (0-99)
    private static final int WORKER_ID;

    // 静态初始化工作机器ID
    static {
        int id;
        try {
            // 尝试根据主机地址获取唯一ID
            InetAddress address = InetAddress.getLocalHost();
            byte[] ipAddresBytes = address.getAddress();
            id = Math.abs(ipAddresBytes[ipAddresBytes.length - 1]) % 100;
        } catch (UnknownHostException e) {
            // 获取失败则使用随机数
            id = ThreadLocalRandom.current().nextInt(100);
        }
        WORKER_ID = id;
    }

    /**
     * 生成默认前缀的订单号
     * @return 订单号
     */
    public static synchronized String generate() {
        return generate(DEFAULT_PREFIX);
    }

    /**
     * 根据指定前缀生成订单号
     * @param prefix 业务前缀
     * @return 订单号
     */
    public static synchronized String generate(String prefix) {
        StringBuilder sb = new StringBuilder();

        // 添加业务前缀
        sb.append(prefix);

        // 添加时间戳 (精确到毫秒)
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        sb.append(timestamp);

        // 添加工作机器ID (保证分布式环境下的唯一性)
        sb.append(String.format("%02d", WORKER_ID));

        // 添加随机数 (使用ThreadLocalRandom提高并发性能)
        sb.append(generateRandomNumber(RANDOM_LENGTH));

        // 添加序列号
        int sequence = SEQUENCE.getAndIncrement();
        if (SEQUENCE.get() > 999999) {
            SEQUENCE.set(1); // 超过最大值时重置
        }
        sb.append(String.format("%0" + SEQUENCE_LENGTH + "d", sequence));

        return sb.toString();
    }

    /**
     * 生成基于雪花算法的订单号（适用于超高并发）
     * @param prefix 业务前缀
     * @return 订单号
     */
    public static synchronized String generateSnowflake(String prefix) {
        StringBuilder sb = new StringBuilder();

        // 添加业务前缀
        sb.append(prefix);

        // 获取当前时间戳
        long timestamp = System.currentTimeMillis();

        // 如果与上次时间相同，则序列号递增
        if (timestamp == lastTimestamp) {
            // 序列号递增
            int sequence = SEQUENCE.getAndIncrement();
            if (SEQUENCE.get() > 999999) {
                SEQUENCE.set(1);
            }
            // 添加时间戳
            sb.append(timestamp);
            // 添加工作机器ID
            sb.append(String.format("%02d", WORKER_ID));
            // 添加序列号
            sb.append(String.format("%0" + SEQUENCE_LENGTH + "d", sequence));
        } else {
            // 时间戳改变，序列重置
            SEQUENCE.set(1);
            lastTimestamp = timestamp;

            // 添加时间戳
            sb.append(timestamp);
            // 添加工作机器ID
            sb.append(String.format("%02d", WORKER_ID));
            // 添加序列号
            sb.append(String.format("%0" + SEQUENCE_LENGTH + "d", 1));
        }

        return sb.toString();
    }

    /**
     * 生成完全无冲突的UUID订单号(最安全，但较长)
     * @param prefix 业务前缀
     * @return 基于UUID的订单号
     */
    public static String generateUUID(String prefix) {
        return prefix + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成指定长度的随机数字
     * @param length 长度
     * @return 随机数字字符串
     */
    private static String generateRandomNumber(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(ThreadLocalRandom.current().nextInt(10));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        System.out.println(generate());
    }
}