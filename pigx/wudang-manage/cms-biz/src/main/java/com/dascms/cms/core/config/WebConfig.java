package com.dascms.cms.core.config;

import com.pig4cloud.pigx.admin.api.feign.RemoteUserService;
import com.dascms.cms.core.service.ConfigService;
import com.dascms.cms.core.service.SiteService;
import com.dascms.cms.core.web.support.FrontendApiInterceptor;
import com.dascms.cms.core.web.support.FrontendInterceptor;
import com.dascms.cms.core.web.support.UrlRedirectInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.mobile.device.DeviceResolver;

@Configuration
public class WebConfig {

    @Autowired
    @Lazy
    private SiteService siteService;
//
    @Autowired
    private ConfigService configService;
//    @Autowired
//    private Props props;
    @Autowired
    private DeviceResolver deviceResolver;
    @Autowired
    @Lazy
    private RemoteUserService remoteUserService;
//
//
//
//    /**
//     * 后台拦截器
//     */
//    @Bean
//    public BackendInterceptor backendInterceptor() {
//        return new BackendInterceptor(remoteUserService, siteService, null, props);
//    }
//
//    /**
//     * 前台拦截器
//     */
    @Bean
    public FrontendInterceptor frontendInterceptor() {
        return new FrontendInterceptor(remoteUserService, siteService, configService, deviceResolver);
    }
//
//    /**
//     * 前台拦截器
//     */
    @Bean
    public FrontendApiInterceptor frontendApiInterceptor() {
        return new FrontendApiInterceptor(remoteUserService);
    }

    /**
     * URL重写拦截器
     */
    @Bean
    public UrlRedirectInterceptor urlRedirectInterceptor() {
        return new UrlRedirectInterceptor(configService);
    }


}
