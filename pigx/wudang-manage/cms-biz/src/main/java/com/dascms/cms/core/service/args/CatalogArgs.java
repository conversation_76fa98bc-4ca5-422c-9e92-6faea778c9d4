package com.dascms.cms.core.service.args;

import com.dascms.cms.core.web.support.BasePage;

import lombok.Data;
@Data
public class CatalogArgs extends BasePage {
    /**
     * 父级类目id，默认0，也就是查询顶级类目
     */
    private Integer catalogParentId = 0;

    /**
     * 类目状态
     */
    private Integer catalogState;
    /**
     * 类目名称
     */
    private String catalogName;
    /**
     * 类目id--类目编号
     */
    private Integer id;
}
