package com.dascms.cms.core.service.args;

import com.dascms.commons.query.BaseQueryArgs;
import org.springframework.lang.Nullable;

import java.util.HashMap;
import java.util.Map;

/**
 * Action 查询参数
 *
 * <AUTHOR>
 */
public class ActionArgs extends BaseQueryArgs {
    private ActionArgs(Map<String, Object> queryMap) {
        super(queryMap);
    }

    public ActionArgs siteId(@Nullable Integer siteId) {
        if (siteId != null) {
            queryMap.put("EQ_siteId_Long", siteId);
        }
        return this;
    }

    public static ActionArgs of() {
        return of(new HashMap<>(16));
    }

    public static ActionArgs of(Map<String, Object> queryMap) {
        return new ActionArgs(queryMap);
    }
}