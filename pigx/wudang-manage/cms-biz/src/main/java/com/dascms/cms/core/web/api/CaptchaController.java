package com.dascms.cms.core.web.api;

import com.dascms.cms.core.domain.Config;
import com.dascms.cms.core.service.ConfigService;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.commons.captcha.CaptchaToken;
import com.dascms.commons.captcha.CaptchaTokenService;
import com.dascms.commons.captcha.IpLoginAttemptService;
import com.dascms.commons.web.Servlets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 图像验证码 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "CaptchaController", description = "验证码接口")
@RestController
@RequestMapping({UrlConstants.API + "/captcha", UrlConstants.FRONTEND_API + "/captcha"})
public class CaptchaController {
    private final ConfigService configService;
    private final IpLoginAttemptService ipLoginAttemptService;
    private final CaptchaTokenService service;

    public CaptchaController(ConfigService configService, IpLoginAttemptService ipLoginAttemptService,
                             CaptchaTokenService service) {
        this.configService = configService;
        this.ipLoginAttemptService = ipLoginAttemptService;
        this.service = service;
    }

    @Operation(summary = "获取验证码Token")
    @GetMapping()
    public CaptchaToken captchaToken() {
        return service.getCaptchaToken();
    }

    @Operation(summary = "尝试验证码是否正确")
    @GetMapping("/try")
    public boolean tryCaptcha(@Parameter(description = "验证码Token") String token,
                              @Parameter(description = "验证码") String captcha) {
        if (StringUtils.isBlank(token) || StringUtils.isBlank(captcha)) {
            return false;
        }
        return service.tryCaptcha(token, captcha);
    }

    @Operation(summary = "是否显示验证码。当登录错误超过指定次数后，需要输入验证码")
    @GetMapping("/is-display")
    public boolean isDisplayCaptcha(HttpServletRequest request) {
        String ip = Servlets.getRemoteAddr(request);
        Config.Security security = configService.getUnique().getSecurity();
        return !security.isTwoFactor() && ipLoginAttemptService.isExcessive(ip, security.getIpCaptchaAttempts());
    }
}
