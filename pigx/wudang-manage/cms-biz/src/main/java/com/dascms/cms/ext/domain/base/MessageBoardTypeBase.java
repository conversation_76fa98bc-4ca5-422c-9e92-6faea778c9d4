package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class MessageBoardTypeBase extends Model<MessageBoardTypeBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "message_board_type";

    /**
     * 留言类别ID
     */
    @NotNull
    @Schema(description="留言类别ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @Nullable
    @Schema(description="站点ID")
    private Long siteId;

    /**
     * 名称
     */
    @Length(max = 30)
    @Nullable
    @Schema(description="名称")
    private String name;

    /**
     * 描述
     */
    @Length(max = 300)
    @Nullable
    @Schema(description="描述")
    private String description;

    /**
     * 排列顺序
     */
    @NotNull
    @Schema(description="排列顺序")
    private Long order = 0L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Nullable
    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(@Nullable Long siteId) {
        this.siteId = siteId;
    }

    @Nullable
    public String getName() {
        return name;
    }

    public void setName(@Nullable String name) {
        this.name = name;
    }

    @Nullable
    public String getDescription() {
        return description;
    }

    public void setDescription(@Nullable String description) {
        this.description = description;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }
}