package com.dascms.cms.core.component;

import com.dascms.cms.core.support.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class TemplateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TemplateService.class);
    private final ResourceLoader resourceLoader;

    public TemplateService(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    public List<String> getTemplates(String theme, String startWitch) throws IOException {
        LOGGER.info("template_theme:{}", theme);
        LOGGER.info("template_startWitch:{}", startWitch);
        List<String> themeList = new ArrayList<>();
        File file = new File(theme);
        if (!file.exists()) {
            return themeList;
        }
        File[] themeFiles = file.listFiles((dir, name) ->
                name.startsWith(startWitch) && name.endsWith(Constants.TEMPLATE_SUFFIX));
        if (themeFiles == null) {
            return themeList;
        }
        for (File themeFile : themeFiles) {
            String name = themeFile.getName();
            themeList.add(themeFile.getName().substring(0, name.indexOf(Constants.TEMPLATE_SUFFIX)));
        }
        return themeList;
    }
}
