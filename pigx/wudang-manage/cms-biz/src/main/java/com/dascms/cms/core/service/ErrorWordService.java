package com.dascms.cms.core.service;

import com.dascms.cms.core.domain.ErrorWord;
import com.dascms.cms.core.domain.SensitiveWord;
import com.dascms.cms.core.domain.base.ErrorWordBase;
import com.dascms.cms.core.mapper.ErrorWordMapper;
import com.dascms.cms.core.service.args.ErrorWordArgs;
import com.dascms.cms.core.web.request.ErrorWordQry;
import com.dascms.commons.db.identifier.SnowflakeSequence;
import com.dascms.commons.query.QueryInfo;
import com.dascms.commons.query.QueryParser;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.pig4cloud.pigx.common.core.util.R;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 易错词 Service
 *
 * <AUTHOR>
 */
@Service
public class ErrorWordService {
    private final ErrorWordMapper mapper;
    private final SnowflakeSequence snowflakeSequence;

    public ErrorWordService(ErrorWordMapper mapper, SnowflakeSequence snowflakeSequence) {
        this.mapper = mapper;
        this.snowflakeSequence = snowflakeSequence;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(ErrorWord bean) {
        bean.setId(snowflakeSequence.nextId());
        mapper.insert(bean);
    }

    @Transactional(rollbackFor = Exception.class)
    public R update(ErrorWord bean) {
        Integer exists = mapper.checkSame(bean.getName(), bean.getId());
        if (exists != null) {
            throw new RuntimeException("错词已存在");
        }
        return R.ok(mapper.update(bean));
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return mapper.delete(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(List<Long> ids) {
        return ids.stream().filter(Objects::nonNull).mapToInt(this::delete).sum();
    }

    @Nullable
    public ErrorWord select(Long id) {
        return mapper.select(id);
    }

    public List<ErrorWord> selectList(ErrorWordArgs args) {
        QueryInfo queryInfo = QueryParser.parse(args.getQueryMap(), ErrorWordBase.TABLE_NAME, "id_desc");
        return mapper.selectAll(queryInfo);
    }

    public List<ErrorWord> selectList(ErrorWordArgs args, int offset, int limit) {
        return PageMethod.offsetPage(offset, limit, false).doSelectPage(() -> selectList(args));
    }

    public Page<ErrorWord> selectPage(ErrorWordArgs args, int page, int pageSize) {
        return PageMethod.startPage(page, pageSize).doSelectPage(() -> selectList(args));
    }

    public Page<SensitiveWord> selectPage(ErrorWordQry errorWordQry) {
        return PageMethod.startPage(errorWordQry.getPageNum(), errorWordQry.getPageSize()).doSelectPage(() -> mapper.selectPage(errorWordQry));
    }

    /**
     * 添加错词
     *
     * @param errorWord
     * @return
     */
    public R add(ErrorWord errorWord) {
        Integer exists = mapper.checkSame(errorWord.getName(), null);
        if (exists != null) {
            throw new RuntimeException("错词已存在");
        }
        return R.ok(mapper.insert(errorWord));
    }
}