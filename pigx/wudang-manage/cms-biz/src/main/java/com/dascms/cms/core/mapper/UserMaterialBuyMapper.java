package com.dascms.cms.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.material.UserMaterialBuy;
import com.dascms.cms.core.service.args.OrderArgs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Repository
@Mapper
public interface UserMaterialBuyMapper extends BaseMapper<UserMaterialBuy> {

    /**
     * 查询我购买的素材列表
     *
     * @param args
     * @return
     */
    List<UserMaterialBuy> mylist(OrderArgs args);

    /**
     * 统计购买的素材数量
     * @param userId
     * @return
     */
    Integer countByUserIdAndType(@Param("userId") Long userId, @Param("type") Integer type);

    /**
     * 根据订单号查询
     * @param orderNos
     * @return
     */
    List<UserMaterialBuy> listByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 根据md5查询
     * @param md5
     * @return
     */
    UserMaterialBuy getByMd5(String md5);
}
