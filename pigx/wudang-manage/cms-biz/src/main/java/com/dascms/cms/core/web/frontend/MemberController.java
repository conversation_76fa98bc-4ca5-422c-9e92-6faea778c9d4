package com.dascms.cms.core.web.frontend;

import com.dascms.cms.core.aop.annotations.RequiresLogin;
import com.dascms.cms.core.domain.Config;
import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.service.ConfigService;
import com.dascms.cms.core.service.UserService;
import com.dascms.cms.core.support.Constants;
import com.dascms.cms.core.support.LoginUserUtil;
import com.dascms.cms.core.web.support.SiteResolver;
import com.dascms.commons.web.exception.Http404Exception;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static com.dascms.cms.core.support.Constants.TARGET_USER;

/**
 * 会员 Controller
 *
 * <AUTHOR>
 */
@Controller("frontendMemberController")
public class MemberController {
    private final UserService userService;
    private final ConfigService configService;
    private final SiteResolver siteResolver;

    @Value("${cms.login.page}")
    private String loginPage;

    public MemberController(UserService userService, ConfigService configService, SiteResolver siteResolver) {
        this.userService = userService;
        this.configService = configService;
        this.siteResolver = siteResolver;
    }

    private static final String USER_TEMPLATE = "mem_user";
    private static final String LOGIN_TEMPLATE = "mem_login";
    private static final String REGISTER_TEMPLATE = "mem_register";
    private static final String PASSWORD_RESET_TEMPLATE = "mem_password_reset";

    @GetMapping({"/login", "/{subDir:[\\w-]+}/login"})
    public String login(@PathVariable(required = false) String subDir, HttpServletRequest request) {
        Site site = siteResolver.resolve(request, subDir);
        return site.assembleTemplate(site.getTemplateName());
    }

    @GetMapping({"/register", "/{subDir:[\\w-]+}/register"})
    public String register(@PathVariable(required = false) String subDir, HttpServletRequest request) {
        Site site = siteResolver.resolve(request, subDir);
        Config.Register register = configService.getUnique().getRegister();
        if (!register.isEnabled()) {
            throw new Http404Exception("error.registerNotEnabled");
        }
        return site.assembleTemplate(REGISTER_TEMPLATE);
    }

    @GetMapping({"/password-reset", "/{subDir:[\\w-]+}/password-reset"})
    public String passwordReset(@PathVariable(required = false) String subDir, HttpServletRequest request) {
        Site site = siteResolver.resolve(request, subDir);
        return site.assembleTemplate(PASSWORD_RESET_TEMPLATE);
    }

    @RequiresLogin
    @GetMapping({"/users/{id}", "/{subDir:[\\w-]+}/users/{id}"})
    public String users(@PathVariable Long id, @PathVariable(required = false) String subDir,
                        HttpServletRequest request, Map<String, Object> modelMap) {
        User loginUser = LoginUserUtil.getLoginUser(request);
        if(loginUser==null){
            return loginPage;
        }
        request.getSession().setAttribute(Constants.GUEST_USERNAME, loginUser);
        Site site = siteResolver.resolve(request, subDir);
        modelMap.put(TARGET_USER, loginUser);
        return site.assembleTemplate(USER_TEMPLATE);
    }
}
