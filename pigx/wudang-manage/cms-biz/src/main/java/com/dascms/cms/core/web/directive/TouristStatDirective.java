package com.dascms.cms.core.web.directive;

import com.dascms.cms.CmsBackendlication;
import com.dascms.cms.core.domain.TouristStat;
import com.dascms.cms.core.service.TouristStatService;
import com.dascms.commons.freemarker.Freemarkers;
import freemarker.core.Environment;
import freemarker.template.TemplateDirectiveBody;
import freemarker.template.TemplateDirectiveModel;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 游客统计
 */
public class TouristStatDirective implements TemplateDirectiveModel {
    @Override
    public void execute(Environment env, Map map, TemplateModel[] loopVars, TemplateDirectiveBody body) throws TemplateException, IOException {
        Freemarkers.requireLoopVars(loopVars);
        Freemarkers.requireBody(body);
        TouristStatService articleService = CmsBackendlication.applicationContext.getBean(TouristStatService.class);
        List<TouristStat> latestTwoYearsData = articleService.getLatestTwoYearsData();
        loopVars[0] = env.getObjectWrapper().wrap(latestTwoYearsData);
        body.render(env.getOut());
    }
}
