package com.dascms.cms.ext.web.frontend;

import com.dascms.cms.core.aop.annotations.RequiresLogin;
import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.support.Frontends;
import com.dascms.cms.core.support.LoginUserUtil;
import com.dascms.cms.core.web.support.SiteResolver;
import com.dascms.cms.ext.domain.MessageBoard;
import com.dascms.cms.ext.service.MessageBoardService;
import com.dascms.commons.web.exception.Http401Exception;
import com.dascms.commons.web.exception.Http403Exception;
import com.dascms.commons.web.exception.Http404Exception;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static com.dascms.cms.core.support.Frontends.PAGE;
import static com.dascms.cms.core.support.Frontends.PAGE_SIZE;

/**
 * 留言板 Controller
 *
 * <AUTHOR>
 */
@Controller("frontendMessageBoardController")
public class MessageBoardController {
    private final SiteResolver siteResolver;
    private final MessageBoardService service;

    public MessageBoardController(SiteResolver siteResolver, MessageBoardService service) {
        this.siteResolver = siteResolver;
        this.service = service;
    }

    private static final String TEMPLATE = "sys_message_board";
    private static final String TEMPLATE_FORM = "sys_message_board_form";
    private static final String TEMPLATE_ITEM = "sys_message_board_item";

    @RequiresLogin
    @GetMapping({"/message-board", "/{subDir:[\\w-]+}/message-board"})
    public String index(@PathVariable(required = false) String subDir, HttpServletRequest request, Map<String, Object> modelMap, @RequestParam(required = false) Integer page) {
        Site site = siteResolver.resolve(request, subDir);
        validateEnabled(site);
        MessageBoard messageBoard = new MessageBoard();
        messageBoard.setSite(site);
        modelMap.put(PAGE, page == null ? 1 : page);
        modelMap.put(PAGE_SIZE, site.getPageSize());
        modelMap.put(Frontends.PAGE_URL_RESOLVER, messageBoard);
        return site.assembleTemplate(TEMPLATE);
    }

    @RequiresLogin
    @GetMapping({"/message-board/create", "/{subDir:[\\w-]+}/message-board/create"})
    public String create(@PathVariable(required = false) String subDir, HttpServletRequest request) {
        Site site = siteResolver.resolve(request, subDir);
        validateEnabled(site);
        validateLoginRequired(site, request);
        return site.assembleTemplate(TEMPLATE_FORM);
    }

    @RequiresLogin
    @GetMapping({"/message-board/{id}", "/{subDir:[\\w-]+}/message-board/{id}"})
    public String show(@PathVariable(required = false) String subDir, @PathVariable Long id,
                       HttpServletRequest request, Map<String, Object> modelMap) {
        Site site = siteResolver.resolve(request, subDir);
        validateEnabled(site);
        MessageBoard messageBoard = service.select(id);
        if (messageBoard == null) {
            throw new Http404Exception("MessageBoard not found. ID=" + id);
        }
        modelMap.put("messageBoard", messageBoard);
        return site.assembleTemplate(TEMPLATE_ITEM);
    }

    private void validateEnabled(Site site) {
        if (!site.getMessageBoard().isEnabled()) {
            throw new Http403Exception("MessageBoard is not enabled.");
        }
    }

    private void validateLoginRequired(Site site, HttpServletRequest request) {
        User user = LoginUserUtil.getLoginUser(request);
        if (site.getMessageBoard().isLoginRequired() && user == null) {
            throw new Http401Exception("MessageBoard login required.");
        }
    }

}
