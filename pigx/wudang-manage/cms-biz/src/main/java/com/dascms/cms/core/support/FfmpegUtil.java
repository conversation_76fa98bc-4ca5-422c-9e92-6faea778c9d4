package com.dascms.cms.core.support;

import net.bramp.ffmpeg.FFmpeg;
import net.bramp.ffmpeg.FFmpegExecutor;
import net.bramp.ffmpeg.FFprobe;
import net.bramp.ffmpeg.builder.FFmpegBuilder;
import net.bramp.ffmpeg.probe.FFmpegFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

/**
 * 音视频工具类
 */
@Component
public class FfmpegUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(FfmpegUtil.class);

    @Value("${ffmpeg.path}")
    private String ffmpegPath;

    @Value("${ffprobe.path}")
    private String ffprobePath;

    private FFmpeg ffmpeg;
    private FFprobe ffprobe;
    private FFmpegExecutor executor;

    @PostConstruct
    public void init() {
//        try {
//            ffmpeg = new FFmpeg(ffmpegPath);
//            ffprobe = new FFprobe(ffprobePath);
//            executor = new FFmpegExecutor(ffmpeg, ffprobe);
//        } catch (IOException e) {
//            throw new RuntimeException("初始化FFmpeg失败", e);
//        }
    }
    /**
     * 视频转码
     */

    /**
     * 音视频截取
     */
    public void trimVideo(String inputPath, String outputPath, long duration, TimeUnit timeUnit) throws Exception {
        // FFmpeg路径，需要在系统中安装FFmpeg
        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(inputPath)           // 输入文件
                .overrideOutputFiles(true)     // 覆盖输出文件
                .addOutput(outputPath)         // 输出文件
                .setDuration(duration, timeUnit)               // 设置时长为30秒
                .done();                       // 完成构建
        // 执行FFmpeg命令
        executor.createJob(builder).run();
        LOGGER.info("音视频截取完成：" + outputPath);
    }

    public void extractFirstFrameFromVideo(String videoPath, String outputImagePath) {
        // FFmpeg路径，需要在系统中安装FFmpeg
        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(videoPath)           // 输入视频
                .overrideOutputFiles(true)     // 覆盖输出文件
                .addOutput(outputImagePath)    // 输出图像路径
                .setFrames(1)                  // 只提取1帧
                .disableAudio()                // 禁用音频
                .setStartOffset(0, TimeUnit.SECONDS)             // 从开始处
                .done();                       // 完成构建

        // 执行FFmpeg命令
        executor.createJob(builder).run();
        LOGGER.info("第一帧图片：" + outputImagePath);
    }

    /**
     * 音视频截取:默认截取30秒
     */
    public void trimVideo(String inputPath, String outputPath) {
        try {
            trimVideo(inputPath, outputPath, 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 显示文件元数据
     */
    private void displayMetadata(String filePath) throws Exception {
        LOGGER.info("\n验证元数据:");
        FFmpegFormat format = ffprobe.probe(filePath).getFormat();
        if (format.tags != null && !format.tags.isEmpty()) {
            format.tags.forEach((key, value) -> {
                LOGGER.info(key + ": " + value);
            });
        } else {
            LOGGER.info("未找到元数据");
            // 使用命令行直接检查
            LOGGER.info("\n使用命令行直接检查:");
            try {
                ProcessBuilder pb = new ProcessBuilder(
                        ffprobePath,
                        "-v", "quiet",
                        "-print_format", "json",
                        "-show_format",
                        filePath
                );
                Process process = pb.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                StringBuilder output = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                LOGGER.info(output.toString());
            } catch (Exception e) {
                LOGGER.error("查看元数据异常", e);
            }
        }
    }

    /**
     * 处理视频文件元数据
     */
    public void processVideoMetadata(String inputFile, String outputFile, String orderNo) throws Exception {
        // 创建FFmpeg命令构建器
        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(inputFile)
                .overrideOutputFiles(true)
                .addOutput(outputFile)
                .setFormat("mp4")
                // 添加元数据
                .addMetaTag("comment", orderNo)
                .setVideoCodec("copy")
                .setAudioCodec("copy")
                .done();
        // 执行FFmpeg命令
        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
        LOGGER.info("视频元数据已成功写入: " + outputFile);
        // 读取并显示元数据
        displayMetadata(outputFile);
    }

    /**
     * 处理音频文件元数据
     */
    public void processAudioMetadata(String inputFile, String outputFile, String orderNo) throws Exception {
        // 创建FFmpeg命令构建器
        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(inputFile)
                .overrideOutputFiles(true)
                .addOutput(outputFile)
                .setFormat("mp3")
                // 添加元数据
                .addMetaTag("comment", orderNo)
                // ID3v2 版本设置
                .addExtraArgs("-id3v2_version", "3")
                .setAudioCodec("copy")
                .done();

        // 执行FFmpeg命令
        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
        LOGGER.info("音频元数据已成功写入: " + outputFile);
        // 读取并显示元数据
        displayMetadata(outputFile);
    }

    /**
     * 读取指定文件中的订单号（假设存储在album或comment字段）
     */
    public String getOrderNo(String filePath) throws Exception {
        FFmpegFormat format = ffprobe.probe(filePath).getFormat();
        if (format.tags != null) {
            if (format.tags.containsKey("comment")) {
                String comment = format.tags.get("comment");
                return comment;
            }
        }
        return null; // 未找到订单号
    }
}
