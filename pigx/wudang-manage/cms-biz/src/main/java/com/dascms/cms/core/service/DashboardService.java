package com.dascms.cms.core.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.xml.catalog.CatalogManager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.dascms.cms.core.domain.catalog.Catalog;
import com.dascms.cms.core.domain.dashboard.StasticsItem;
import com.dascms.cms.core.mapper.OrderMapper;
import com.dascms.cms.core.mapper.UjcmsMaterialMapper;
import com.dascms.cms.core.service.args.DashboardArgs;
import com.dascms.cms.ext.mapper.CatalogMapper;
import com.pig4cloud.pigx.common.core.util.DateUtil;
import com.pig4cloud.pigx.common.core.util.MathUtil;

@Service
public class DashboardService {

    @Autowired
    private UjcmsMaterialMapper ujcmsmatearialmapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private CatalogMapper catalogmapper;

    public ResponseEntity<List<StasticsItem>> summary(DashboardArgs args) {
        List<StasticsItem> list = new ArrayList<StasticsItem>();
        // 当前值
        Integer currentVal = 0;
        // 前值
        Integer preVal = 0;
        // 素材总数
        StasticsItem item1 = new StasticsItem();
        item1.setName("素材总数");
        currentVal = ujcmsmatearialmapper.count(args.getStartTime(), args.getEndTime());
        item1.setValue(new BigDecimal(currentVal == null ? 0 : currentVal));
        preVal = ujcmsmatearialmapper.count(args.getRatioStartTime(), args.getStartTime());
        item1.setPreValue(new BigDecimal(preVal == null ? 0 : preVal));
        item1.setRatio(MathUtil.div(item1.getValue().subtract(item1.getPreValue()), item1.getPreValue(),
                MathUtil.DEFAULT_SCALE));
        list.add(item1);
        // 交易素材
        StasticsItem item2 = new StasticsItem();
        item2.setName("交易素材");
        currentVal = orderMapper.count(args.getStartTime(), args.getEndTime());
        item2.setValue(new BigDecimal(currentVal == null ? 0 : currentVal));
        preVal = orderMapper.count(args.getRatioStartTime(), args.getStartTime());
        item2.setPreValue(new BigDecimal(preVal == null ? 0 : preVal));
        item2.setRatio(MathUtil.div(item2.getValue().subtract(item2.getPreValue()), item2.getPreValue(),
                MathUtil.DEFAULT_SCALE));
        list.add(item2);
        // 总访问量
        StasticsItem item3 = new StasticsItem();
        item3.setName("总访问量");
        currentVal = ujcmsmatearialmapper.countAccess(args.getStartTime(), args.getEndTime());
        item3.setValue(new BigDecimal(currentVal == null ? 0 : currentVal));
        preVal = ujcmsmatearialmapper.countAccess(args.getRatioStartTime(), args.getStartTime());
        item3.setPreValue(new BigDecimal(preVal == null ? 0 : preVal));
        item3.setRatio(MathUtil.div(item3.getValue().subtract(item3.getPreValue()), item3.getPreValue(),
                MathUtil.DEFAULT_SCALE));
        list.add(item3);
        // 总交易额
        StasticsItem item4 = new StasticsItem();
        item4.setName("总交易额");
        BigDecimal currentPrice = orderMapper.countPrice(args.getStartTime(), args.getEndTime());
        item4.setValue(currentPrice == null ? new BigDecimal(0) : currentPrice);
        BigDecimal prePrice = orderMapper.countPrice(args.getRatioStartTime(), args.getStartTime());
        item4.setPreValue(prePrice == null ? new BigDecimal(0) : prePrice);
        item4.setRatio(MathUtil.div(item4.getValue().subtract(item4.getPreValue()), item4.getPreValue(),
                MathUtil.DEFAULT_SCALE));
        list.add(item4);
        // 总订单量
        StasticsItem item5 = new StasticsItem();
        item5.setName("总订单量");
        currentVal = orderMapper.countOrder(args.getStartTime(), args.getEndTime());
        item5.setValue(new BigDecimal(currentVal == null ? 0 : currentVal));
        preVal = orderMapper.countOrder(args.getRatioStartTime(), args.getStartTime());
        item5.setPreValue(new BigDecimal(preVal == null ? 0 : preVal));
        item5.setRatio(MathUtil.div(item5.getValue().subtract(item5.getPreValue()), item5.getPreValue(),
                MathUtil.DEFAULT_SCALE));
        list.add(item5);
        // 客单价
        StasticsItem item6 = new StasticsItem();
        item6.setName("客单价");
        item6.setValue(MathUtil.div(item4.getValue(), item5.getValue(), MathUtil.DEFAULT_SCALE));
        item6.setPreValue(MathUtil.div(item4.getPreValue(), item5.getPreValue(), MathUtil.DEFAULT_SCALE));
        item6.setRatio(MathUtil.div(item6.getValue().subtract(item6.getPreValue()), item6.getPreValue(),
                MathUtil.DEFAULT_SCALE));
        list.add(item6);
        return ResponseEntity.ok(list);
    }

    public ResponseEntity<List<StasticsItem>> orderProductType(DashboardArgs args) {
        List<StasticsItem> list = orderMapper.orderProductType(args.getStartTime(), args.getEndTime());
        List<Catalog> catalogs = catalogmapper.selectList(null);
        Map<Integer, String> map = catalogs.parallelStream()
                .collect(Collectors.toMap(Catalog::getId, Catalog::getCatalogName, (k1, k2) -> k1));
        list.forEach(item -> {
            item.setName(map.get(Integer.valueOf(item.getName())));
        });
        return ResponseEntity.ok(list);
    }

    public ResponseEntity<List<StasticsItem>> saleRank(DashboardArgs args) {
        List<StasticsItem> list = orderMapper.saleRank(args.getStartTime(), args.getEndTime());
        return ResponseEntity.ok(list);
    }

    public ResponseEntity<List<StasticsItem>> funnel(DashboardArgs args) {
        List<StasticsItem> list = new ArrayList<StasticsItem>();
        // 总访问量
        StasticsItem item3 = new StasticsItem();
        item3.setName("总访问量");
        Integer access = ujcmsmatearialmapper.countAccess(args.getStartTime(), args.getEndTime());
        item3.setValue(new BigDecimal(access == null ? 0 : access));
        list.add(item3);
        // 提交订单
        StasticsItem item4 = new StasticsItem();
        item4.setName("提交订单");
        Integer order = orderMapper.countOrder(args.getStartTime(), args.getEndTime());
        item4.setValue(new BigDecimal(order == null ? 0 : order));
        list.add(item4);
        // 支付成功
        StasticsItem item5 = new StasticsItem();
        item5.setName("付款");
        Integer pay = orderMapper.countPay(args.getStartTime(), args.getEndTime());
        item5.setValue(new BigDecimal(pay == null ? 0 : pay));
        list.add(item5);
        return ResponseEntity.ok(list);
    }

    public ResponseEntity<List<StasticsItem>> saleTrend(DashboardArgs args) {
        List<StasticsItem> list = orderMapper.saleTrend(args.getStartTime(), args.getEndTime());
        Collections.sort(list, (o1, o2) -> o1.getName().compareTo(o2.getName()));
        return ResponseEntity.ok(list);
    }

    public ResponseEntity<List<StasticsItem>> customerAge(DashboardArgs args) {

        List<StasticsItem> list = new ArrayList<StasticsItem>();
        // 20岁以下
        Date now=new Date();
        Date birthEnd = now;
        Date birthStart=DateUtil.addByDay(now, -20*365);
        StasticsItem item1 = new StasticsItem();
        item1.setName("20岁以下");
        Integer age1 = orderMapper.customerAge(args.getStartTime(), args.getEndTime(), birthStart, birthEnd);
        item1.setValue(new BigDecimal(age1 == null? 0 : age1));
        list.add(item1);
        // 20-30岁
        StasticsItem item2 = new StasticsItem();
        item2.setName("20-30岁");
        birthEnd=DateUtil.addByDay(now, -20*365);
        birthStart=DateUtil.addByDay(now, -30*365);
        Integer age2 = orderMapper.customerAge(args.getStartTime(), args.getEndTime(), birthStart, birthEnd);
        item2.setValue(new BigDecimal(age2 == null? 0 : age2));
        list.add(item2);
        // 30-40岁
        StasticsItem item3 = new StasticsItem();
        item3.setName("30-40岁");
        birthEnd=DateUtil.addByDay(now, -30*365);
        birthStart=DateUtil.addByDay(now, -40*365);
        Integer age3 = orderMapper.customerAge(args.getStartTime(), args.getEndTime(), birthStart, birthEnd);
        item3.setValue(new BigDecimal(age3 == null? 0 : age3));
        list.add(item3);
        // 40岁以上
        StasticsItem item4 = new StasticsItem();
        item4.setName("40岁以上");
        birthEnd=DateUtil.addByDay(now, -40*365);
        birthStart=DateUtil.addByDay(now, -100*365);
        Integer age4 = orderMapper.customerAge(args.getStartTime(), args.getEndTime(), birthStart, birthEnd);
        item4.setValue(new BigDecimal(age4 == null? 0 : age4));
        list.add(item4);
        //总人数
        Integer total=age1+age2+age3+age4;
        item1.setRatio(MathUtil.div(item1.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        item2.setRatio(MathUtil.div(item2.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        item3.setRatio(MathUtil.div(item3.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        item4.setRatio(MathUtil.div(item4.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        return ResponseEntity.ok(list);
    }

    public ResponseEntity<List<StasticsItem>> customerSex(DashboardArgs args) {
        List<StasticsItem> list = new ArrayList<StasticsItem>();
        // 男
        StasticsItem item1 = new StasticsItem();
        item1.setName("男");
        Integer sex1 = orderMapper.customerSex(args.getStartTime(), args.getEndTime(), 1);
        item1.setValue(new BigDecimal(sex1 == null? 0 : sex1));
        list.add(item1);
        // 女
        StasticsItem item2 = new StasticsItem();
        item2.setName("女");
        Integer sex2 = orderMapper.customerSex(args.getStartTime(), args.getEndTime(), 2);
        item2.setValue(new BigDecimal(sex2 == null? 0 : sex2));
        list.add(item2);
        // 总人数
        Integer total = sex1 + sex2;
        item1.setRatio(MathUtil.div(item1.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        item2.setRatio(MathUtil.div(item2.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        return ResponseEntity.ok(list);
    }

    public ResponseEntity<List<StasticsItem>> customerType(DashboardArgs args) {
        List<StasticsItem> list = new ArrayList<StasticsItem>();
        // 新客户--之前没有购买过的客户，在当前时间段首次购买
        StasticsItem item1 = new StasticsItem();
        item1.setName("新客户");
        Integer type1 = orderMapper.customerType(args.getStartTime(), args.getEndTime(),0);
        item1.setValue(new BigDecimal(type1 == null? 0 : type1));
        list.add(item1);
        // 回购客户--之前购买过的客户，在当前时间段再次购买
        StasticsItem item2 = new StasticsItem();
        item2.setName("回购客户");
        Integer type2 = orderMapper.customerType(args.getStartTime(), args.getEndTime(),1);
        item2.setValue(new BigDecimal(type2 == null? 0 : type2));
        list.add(item2);
        // 总人数
        Integer total = type1 + type2;
        item1.setRatio(MathUtil.div(item1.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        item2.setRatio(MathUtil.div(item2.getValue(), new BigDecimal(total), MathUtil.DEFAULT_SCALE));
        return ResponseEntity.ok(list);
    }

}
