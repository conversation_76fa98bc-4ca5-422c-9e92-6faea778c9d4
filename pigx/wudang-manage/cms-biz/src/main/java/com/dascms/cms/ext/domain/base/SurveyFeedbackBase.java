package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pig4cloud.pigx.common.core.deserialize.DefaultLocalDateTimeDeserializer;
import com.pig4cloud.pigx.common.core.deserialize.DefaultLocalDateTimeSerializable;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class SurveyFeedbackBase extends Model<SurveyFeedbackBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "survey_feedback";

    /**
     * 问卷反馈ID
     */
    @NotNull
    @Schema(description="问卷反馈ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @NotNull
    @Schema(description="站点ID")
    private Long siteId = 0L;

    /**
     * 问卷ID
     */
    @NotNull
    @Schema(description="问卷ID")
    private Long surveyId = 0L;

    /**
     * 用户ID
     */
    @Nullable
    @Schema(description="用户ID")
    private Long userId;

    /**
     * IP地址
     */
    @Length(max = 45)
    @Nullable
    @Schema(description="IP地址")
    private String ip;

    /**
     * Cookie
     */
    @Nullable
    @Schema(description="Cookie")
    private Long cookie;

    /**
     * 创建日期
     */
    @NotNull
    @Schema(description="创建日期")
    @JsonDeserialize(using = DefaultLocalDateTimeDeserializer.class)
    @JsonSerialize(using = DefaultLocalDateTimeSerializable.class)
    private LocalDateTime created = LocalDateTime.now();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Nullable
    public Long getUserId() {
        return userId;
    }

    public void setUserId(@Nullable Long userId) {
        this.userId = userId;
    }

    @Nullable
    public String getIp() {
        return ip;
    }

    public void setIp(@Nullable String ip) {
        this.ip = ip;
    }

    @Nullable
    public Long getCookie() {
        return cookie;
    }

    public void setCookie(@Nullable Long cookie) {
        this.cookie = cookie;
    }

    public LocalDateTime getCreated() {
        return created;
    }

    public void setCreated(LocalDateTime created) {
        this.created = created;
    }
}