package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class SurveyOptionBase extends Model<SurveyOptionBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "survey_option";

    /**
     * 问卷选项ID
     */
    @NotNull
    @Schema(description="问卷选项ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @NotNull
    @Schema(description="站点ID")
    private Long siteId = 0L;

    /**
     * 问卷ID
     */
    @NotNull
    @Schema(description="问卷ID")
    private Long surveyId = 0L;

    /**
     * 问卷条目ID
     */
    @NotNull
    @Schema(description="问卷条目ID")
    private Long itemId = 0L;

    /**
     * 标题
     */
    @Length(max = 300)
    @NotNull
    @Schema(description="标题")
    private String title = "";

    /**
     * 得票数
     */
    @NotNull
    @Schema(description="得票数")
    private Integer count = 0;

    /**
     * 排列顺序
     */
    @NotNull
    @Schema(description="排列顺序")
    private Integer order = 999999;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }
}