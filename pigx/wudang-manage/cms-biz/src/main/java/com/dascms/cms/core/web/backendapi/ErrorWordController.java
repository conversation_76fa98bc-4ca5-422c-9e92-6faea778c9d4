package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.domain.ErrorWord;
import com.dascms.cms.core.service.ErrorWordService;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.cms.core.web.request.ErrorWordQry;
import com.pig4cloud.pigx.common.core.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

import static com.dascms.commons.db.MyBatis.springPage;

@Controller
@RequestMapping(UrlConstants.BACKEND_API + "/erroreword")
public class ErrorWordController {

    @Autowired
    private ErrorWordService errorWordService;

    /**
     * 获取目录列表（分类列表）
     */
    @RequestMapping("pageList")
    @ResponseBody
    public Page pageList(ErrorWordQry errorWordQry) {
        return springPage(errorWordService.selectPage(errorWordQry));
    }

    /**
     * 添加敏感词
     *
     * @param errorWord
     * @return
     */
    @RequestMapping("add")
    @ResponseBody
    public R add(@RequestBody ErrorWord errorWord) {
        return errorWordService.add(errorWord);
    }

    /**
     * 更新敏感词
     *
     * @param errorWord
     * @return
     */
    @RequestMapping("update")
    @ResponseBody
    public R update(@RequestBody ErrorWord errorWord) {
        return errorWordService.update(errorWord);
    }

    /**
     * 敏感词详情
     */
    @RequestMapping("detail")
    @ResponseBody
    public R detail(@RequestParam Long id) {
        return R.ok(errorWordService.select(id));
    }

    /**
     * 删除敏感词
     */

    @RequestMapping("delete")
    @ResponseBody
    public R delete(@RequestBody List<Long> ids) {
        errorWordService.delete(ids);
        return R.ok();
    }
}
