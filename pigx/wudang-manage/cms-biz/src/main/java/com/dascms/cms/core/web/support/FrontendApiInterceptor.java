package com.dascms.cms.core.web.support;

import com.pig4cloud.pigx.admin.api.feign.RemoteUserService;
import com.pig4cloud.pigx.common.core.constant.SecurityConstants;
import com.pig4cloud.pigx.common.core.util.R;
import com.dascms.cms.core.support.Contexts;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 前台拦截器
 *
 * <AUTHOR>
 */
public class FrontendApiInterceptor implements HandlerInterceptor {

    private final RemoteUserService remoteUserService;

    public FrontendApiInterceptor(RemoteUserService userService) {
        this.remoteUserService = userService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 当前用户
        Optional.ofNullable(Contexts.findCurrentUserId()).map(userId->{
                   return remoteUserService.getUserById(userId,SecurityConstants.FROM_IN);
                }).map(R::getData)
                .ifPresent(Contexts::setCurrentUser);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                @Nullable Exception ex) {
        Contexts.clearCurrentUser();
        Contexts.clearCurrentSite();
        Contexts.clearMobile();
    }
}
