package com.dascms.cms.core.web.frontend;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dascms.cms.core.domain.*;
import com.dascms.cms.core.domain.count.RelicTypeCount;
import com.dascms.cms.core.service.*;
import com.dascms.cms.core.support.*;
import com.dascms.cms.core.web.support.SiteResolver;
import com.dascms.commons.constants.ChannelConstants;
import com.dascms.commons.file.FileHandler;
import com.dascms.commons.web.PathResolver;
import com.dascms.commons.web.Servlets;
import com.dascms.commons.web.exception.Http403Exception;
import com.dascms.commons.web.exception.Http404Exception;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pig4cloud.pigx.admin.api.entity.SysUser;
import com.pig4cloud.pigx.common.core.constant.CommonConstants;
import com.pig4cloud.pigx.common.core.enums.FileTypeEnum;
import com.pig4cloud.pigx.common.core.util.DateparseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.dascms.cms.core.support.Frontends.PAGE;
import static com.dascms.cms.core.support.Frontends.PAGE_URL_RESOLVER;

/**
 * 前台文章 Controller
 *
 * <AUTHOR>
 */
@Controller("frontendArticleController")
public class ArticleController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ArticleController.class);

    private final ArticleBufferService bufferService;
    private final ArticleService articleService;
    private final ChannelService channelService;
    private final GroupService groupService;
    private final SiteResolver siteResolver;
    private final PathResolver pathResolver;
    private final Props props;

    @Autowired
    private ArticleRelicAttachService articleRelicAttachService;

    public ArticleController(ArticleBufferService bufferService, ArticleService articleService,
                             ChannelService channelService, GroupService groupService,
                             SiteResolver siteResolver, PathResolver pathResolver, Props props) {
        this.bufferService = bufferService;
        this.articleService = articleService;
        this.channelService = channelService;
        this.groupService = groupService;
        this.siteResolver = siteResolver;
        this.pathResolver = pathResolver;
        this.props = props;
    }

    @GetMapping({UrlConstants.ARTICLE + "/{id}", UrlConstants.ARTICLE + "/{id}/{page:[\\d]+}",
            UrlConstants.ARTICLE + "/{id}/{alias:[\\w-]+}", UrlConstants.ARTICLE + "/{id}/{alias:[\\w-]+}/{page:[\\d]+}",
            "/{subDir:[\\w-]+}" + UrlConstants.ARTICLE + "/{id}", "/{subDir:[\\w-]+}" + UrlConstants.ARTICLE + "/{id}_{page:[\\d]+}",
            "/{subDir:[\\w-]+}" + UrlConstants.ARTICLE + "/{id}/{alias:[\\w-]+}",
            "/{subDir:[\\w-]+}" + UrlConstants.ARTICLE + "/{id}/{alias:[\\w-]+}/{page:[\\d]+}"})
    public String article(@PathVariable Long id, @PathVariable(required = false) String subDir,
                          @PathVariable(required = false) String alias, @PathVariable(required = false) Integer page,
                          @RequestParam(defaultValue = "false") boolean preview,
                          HttpServletRequest request, Map<String, Object> modelMap) {
        Site site = siteResolver.resolve(request, subDir);
        SysUser user = Contexts.findCurrentUser();
        Article article = validateArticle(id, site, user, preview);
        //不显示的附件直接过滤掉
        if (!StringUtils.equals(article.getExt().getAlias(), alias)) {
            return "redirect:" + article.getUrl();
        }
        Channel channel = channelService.select(article.getChannelId());
        if (StringUtils.isNotBlank(channel.getAlias())
                && (channel.getAlias().contains(ChannelConstants.ALIAS_RELIC)
                || channel.getAlias().contains(ChannelConstants.ALIAS_ARCHIVE)
        )) {//文物模板
            assignArticle(article, channel, modelMap);
        }
        //根据tag查询的相关文章
        List<Article> relatedArticles = articleService.listRelatedArticles(article.getId());
        if (CollectionUtils.isNotEmpty(relatedArticles)) {
            relatedArticles = relatedArticles.parallelStream().filter(a -> !a.getId().equals(article.getId())).collect(Collectors.toList());
        } else {
            relatedArticles = Collections.emptyList();
        }
        //查询当前登录用户对文章的收藏状态
        setArticleFavoriteStatus(article, request);
        modelMap.put("article", article);
        modelMap.put("relatedArticles", relatedArticles);
        modelMap.put("articles", relatedArticles);
        modelMap.put("channel", channel);
        modelMap.put(PAGE, Constants.validPage(page));
        modelMap.put(PAGE_URL_RESOLVER, article);
        return article.getTemplate();
    }

    /**
     * 查询当前登录用户对文章的收藏状态
     *
     * @param article
     */
    private void setArticleFavoriteStatus(Article article, HttpServletRequest request) {
        User user = LoginUserUtil.getLoginUser(request);
        if (user != null) {
            Integer favorite = articleService.getFavorite(user.getId(), article.getId());
            article.setFavorite(favorite == null ? 0 : favorite);
        } else {//没有登录，默认为未收藏
            article.setFavorite(0);
        }
    }

    /**
     * 填充文物详情数据
     *
     * @param article
     */
    private void assignArticle(Article article, Channel channel, Map<String, Object> modelMap) {
        Model articleModel = channel.getArticleModel();
        String customs = articleModel.getCustoms();
        String customsValue = article.getMainsJson();
        if (StringUtils.isNotBlank(customs) && StringUtils.isNotBlank(customsValue)) {
            JSONObject customsJson = JSONObject.parseObject(customsValue);
            try {
                List<Model.Field> fieldList = Constants.MAPPER.readValue(customs, new TypeReference<List<Model.Field>>() {
                });
                for (Model.Field field : fieldList) {
                    Object o = customsJson.get(field.getCode());
                    field.setValue(o);
                    if (ChannelConstants.DATE.equals(field.getType()) && o != null) {
                        try {
                            LocalDateTime o1 = DateparseUtil.parse((String) o);
                            field.setValue(DateUtil.format(o1, CommonConstants.DATE_TIME_FORMAT));
                        } catch (Exception e) {
                            LOGGER.info("time_parse_exception", o.toString());
                            LOGGER.error("time_parse_exception", e);
                        }
                    }
                }
                modelMap.put("fieldList", fieldList);
                //附件列表和统计
                List<ArticleRelicAttach> articleRelicAttaches = articleRelicAttachService.selectByArticleId(article.getId());
                for (ArticleRelicAttach articleRelicAttach : articleRelicAttaches) {
                    articleRelicAttach.setFileTypeLabel(FileTypeEnum.getName(articleRelicAttach.getFileType().toString()));
                }
                articleRelicAttaches = articleRelicAttaches.parallelStream().filter(a -> CommonConstants.ONE.equals(a.getShowFlag())).collect(Collectors.toList());
                modelMap.put("attachList", articleRelicAttaches);
                //类型统计
                Map<Integer, List<ArticleRelicAttach>> integerListMap = articleRelicAttaches.parallelStream().collect(Collectors.groupingBy(ArticleRelicAttach::getFileType));
                List<RelicTypeCount> relicTypeCounts = new LinkedList<>();
                RelicTypeCount all = new RelicTypeCount();
                all.setLabel("全部");
                all.setTypeId(0);
                all.setTotal(0);
                relicTypeCounts.add(all);
                for (Map.Entry<Integer, List<ArticleRelicAttach>> integerListEntry : integerListMap.entrySet()) {
                    RelicTypeCount relicTypeCount = new RelicTypeCount();
                    relicTypeCount.setTypeId(integerListEntry.getKey());
                    relicTypeCount.setLabel(FileTypeEnum.getName(integerListEntry.getKey().toString()));
                    relicTypeCount.setTotal(integerListEntry.getValue().size());
                    all.setTotal(all.getTotal() + relicTypeCount.getTotal());
                    relicTypeCounts.add(relicTypeCount);
                }
                modelMap.put("countList", relicTypeCounts);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @GetMapping({"/download-file/{id:[\\d]+}", "/download-file/{id:[\\d]+}/{index:[\\d]+}",
            "/{subDir:[\\w-]+}/download-file/{id:[\\d]+}", "/{subDir:[\\w-]+}/download-file/{id:[\\d]+}/{index:[\\d]+}"})
    public void download(@PathVariable Long id, @PathVariable(required = false) Integer index,
                         @PathVariable(required = false) String subDir,
                         @RequestParam long time, @NotNull String key,
                         @RequestParam(defaultValue = "false") boolean preview,
                         HttpServletRequest request, HttpServletResponse response) throws IOException {
        Site site = siteResolver.resolve(request, subDir);
        if (!Utils.validateDownloadKey(key, id, time, props.getDownloadSecret())) {
            throw new Http403Exception("Download Key Invalid");
        }
        int expires = 24 * 60 * 60 * 1000;
        if (System.currentTimeMillis() - time > expires) {
            throw new Http403Exception("Download Key Expired");
        }
        SysUser user = Contexts.findCurrentUser();
        Article article = validateArticle(id, site, user, preview);
        String fileUrl;
        String fileName;
        if (index != null) {
            List<Article.ArticleFile> fileList = article.getFileList();
            if (index >= fileList.size()) {
                throw new Http404Exception("Article file not found. id=" + id + ", index=" + index);
            }
            Article.ArticleFile articleFile = fileList.get(index);
            fileUrl = articleFile.getUrl();
            fileName = articleFile.getName();
        } else {
            fileUrl = article.getFile();
            fileName = article.getFileName();
        }
        if (StringUtils.isBlank(fileUrl)) {
            throw new Http404Exception("Article file not exist. id=" + id);
        }
        ArticleBuffer buffer = ArticleBuffer.of(article);
        int downloads = buffer.getDownloads() + 1;
        buffer.setDownloads(downloads);
        bufferService.update(buffer);
        FileHandler fileHandler = site.getConfig().getUploadStorage().getFileHandler(pathResolver);
        String filename = fileHandler.getName(fileUrl);
        // 不属于当前存储点（外部文件或上传文件后修改了储存点），直接重定向至文件
        if (filename == null) {
            response.sendRedirect(fileUrl);
            return;
        }
        if (!fileHandler.isFile(filename)) {
            throw new Http404Exception("Article file not found: " + fileUrl);
        }
        Servlets.setAttachmentHeader(request, response,
                Optional.ofNullable(fileName).filter(StringUtils::isNotBlank)
                        .orElseGet(() -> FilenameUtils.getName(fileUrl)));
        try (OutputStream output = response.getOutputStream()) {
            fileHandler.writeOutputStream(filename, output);
        }
    }

    private Article validateArticle(Long id, Site site, SysUser user, boolean preview) {
        Article article = articleService.select(id);
        if (article == null) {
            throw new Http404Exception("Article not found. ID=" + id);
        }
        if (!site.getId().equals(article.getSiteId())) {
            throw new Http404Exception("error.notInSite",
                    String.valueOf(article.getSiteId()), String.valueOf(site.getId()));
        }
        //checkAccessPermission(article, user, groupService, channelService, preview);
        return article;
    }
}
