package com.dascms.cms.core.web.backendapi;

import com.pig4cloud.pigx.admin.api.entity.SysUser;
import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.generator.HtmlGenerator;
import com.dascms.cms.core.generator.HtmlService;
import com.dascms.cms.core.generator.LuceneGenerator;
import com.dascms.cms.core.support.Contexts;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.commons.web.Responses;
import com.dascms.commons.web.Responses.Body;
import com.dascms.commons.web.Servlets;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 生成器 Controller
 *
 * <AUTHOR>
 */
@RestController("backendGeneratorController")
@RequestMapping(UrlConstants.BACKEND_API + "/core/generator")
public class GeneratorController {
    private final HtmlService htmlService;
    private final HtmlGenerator htmlGenerator;
    private final LuceneGenerator luceneGenerator;

    public GeneratorController(HtmlService htmlService, HtmlGenerator htmlGenerator, LuceneGenerator luceneGenerator) {
        this.htmlService = htmlService;
        this.htmlGenerator = htmlGenerator;
        this.luceneGenerator = luceneGenerator;
    }

    @PostMapping("fulltext-reindex-all")
    //@PreAuthorize("hasAnyAuthority('generator:fulltext:reindexAll','*')")
    //@OperationLog(module = "fulltext", operation = "reindexAll", type = OperationType.UPDATE)
    public ResponseEntity<Body> fulltextReindexAll(HttpServletRequest request) {
        Site site = Contexts.getCurrentSite();
        SysUser user = Contexts.getCurrentUser();
        String taskName = Servlets.getMessage(request, "task.fulltext.all");
        luceneGenerator.reindex(site.getId(), user.getUserId(), taskName);
        return Responses.ok();
    }

    @PostMapping("fulltext-reindex-site")
    //@PreAuthorize("hasAnyAuthority('generator:fulltext:reindexSite','*')")
    //@OperationLog(module = "fulltext", operation = "reindexSite", type = OperationType.UPDATE)
    public ResponseEntity<Body> fulltextSiteReindex(HttpServletRequest request) {
        Site site = Contexts.getCurrentSite();
        SysUser user = Contexts.getCurrentUser();
        String taskName = Servlets.getMessage(request, "task.fulltext.site");
        luceneGenerator.reindex(site.getId(), user.getUserId(), taskName, site.getId());
        return Responses.ok();
    }

    @PostMapping("html-all")
    //@PreAuthorize("hasAnyAuthority('generator:html','*')")
    //@OperationLog(module = "html", operation = "updateAll", type = OperationType.UPDATE)
    public ResponseEntity<Body> updateAllHtml(HttpServletRequest request) {
        Site site = Contexts.getCurrentSite();
        SysUser user = Contexts.getCurrentUser();
        String taskName = Servlets.getMessage(request, "task.html.all");
        htmlGenerator.updateAllHtml(site.getId(), user.getUserId(), taskName, site);
        return Responses.ok();
    }

    @PostMapping("html-home")
    //@PreAuthorize("hasAnyAuthority('generator:html','*')")
    //@OperationLog(module = "html", operation = "updateHome", type = OperationType.UPDATE)
    public ResponseEntity<Body> updateHomeHtml() {
        Site site = Contexts.getCurrentSite();
        htmlService.updateHomeHtml(site);
        return Responses.ok();
    }

    @PostMapping("html-channel")
    //@PreAuthorize("hasAnyAuthority('generator:html','*')")
    //@OperationLog(module = "html", operation = "updateChannel", type = OperationType.UPDATE)
    public ResponseEntity<Body> updateChannelHtml(HttpServletRequest request) {
        Site site = Contexts.getCurrentSite();
        SysUser user = Contexts.getCurrentUser();
        String taskName = Servlets.getMessage(request, "task.html.channel");
        htmlGenerator.updateChannelHtml(site.getId(), user.getUserId(), taskName, site);
        return Responses.ok();
    }

    @PostMapping("html-article")
    //@PreAuthorize("hasAnyAuthority('generator:html','*')")
    //@OperationLog(module = "html", operation = "updateArticle", type = OperationType.UPDATE)
    public ResponseEntity<Body> updateArticleHtml(HttpServletRequest request) {
        Site site = Contexts.getCurrentSite();
        SysUser user = Contexts.getCurrentUser();
        String taskName = Servlets.getMessage(request, "task.html.article");
        htmlGenerator.updateArticleHtml(site.getId(), user.getUserId(), taskName, site);
        return Responses.ok();
    }

}