package com.dascms.cms.core.wechatpay.request;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.SecureRandom;
import java.util.Formatter;

public class HexdumpRandomBuffer {
    /**
     * 生成随机字符串
     *
     * @return
     */
    public static String getString() {
        // 创建安全随机数生成器
        SecureRandom secureRandom = new SecureRandom();
        // 分配16字节缓冲区
        byte[] bytes = new byte[16];
        // 生成随机数据
        secureRandom.nextBytes(bytes);
        // 使用ByteBuffer处理数据
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        buffer.order(ByteOrder.BIG_ENDIAN); // 设置字节序为大端序
        // 输出格式化结果
        StringBuilder result = new StringBuilder();
        while (buffer.hasRemaining() && buffer.remaining() >= 4) {
            int value = buffer.getInt();
            result.append(String.format("%08X", value));
        }
        return result.toString().trim();
    }

    /**
     * 与上面的方法是一样的效果，随便调用哪个都行，但最好调用的时候都用同一个方法，避免混淆。
     *
     * @return
     */
    public static String getStringV1() {
        // 创建 SecureRandom 实例
        SecureRandom random = new SecureRandom();
        // 分配 16 字节数组
        byte[] data = new byte[16];
        random.nextBytes(data);
        // 格式化输出：每 4 字节一组，共 4 组，最后换行
        try (Formatter formatter = new Formatter()) {
            for (int i = 0; i < data.length; i += 4) {
                int value = 0;
                for (int j = 0; j < 4; j++) {
                    value <<= 8;
                    value |= data[i + j] & 0xFF;
                }
                formatter.format("%08X", value); // 添加了空格
            }
            return formatter.toString().trim(); // trim去掉最后多余的空格
        }
    }

    public static void main(String[] args) {
        System.err.println(getString());
        System.err.println(getStringV1());
    }
}
