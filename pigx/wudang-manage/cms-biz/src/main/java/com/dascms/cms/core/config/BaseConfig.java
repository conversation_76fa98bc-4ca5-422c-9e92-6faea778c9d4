package com.dascms.cms.core.config;

import com.nimbusds.jose.KeyLengthException;
import com.dascms.cms.core.support.Props;
import com.dascms.cms.core.web.support.ExceptionResolver;
import com.dascms.commons.security.jwt.HmacSm3JwsSigner;
import com.dascms.commons.security.jwt.HmacSm3JwsVerifier;
import com.dascms.commons.security.jwt.JwtProperties;
import com.dascms.commons.web.DirectoryRedirectInterceptor;
import com.dascms.commons.web.TimerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

@Configuration
public class BaseConfig {
    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private Props props;
    /**
     * 异常处理
     */
    @Bean
    public ExceptionResolver exceptionResolver() {
        return new ExceptionResolver();
    }

    /**
     * 计时器拦截器
     */
    @Bean
    public TimerInterceptor timerInterceptor() {
        return new TimerInterceptor();
    }

    /**
     * JWT 配置
     */
    @Bean
    public JwtProperties jwtProperties() {
        return new JwtProperties();
    }

    /**
     * JWT 签名。使用国密 HmacSM3。
     */
    @Bean
    public HmacSm3JwsSigner hmacSm3JwsSigner() throws KeyLengthException {
        return new HmacSm3JwsSigner(jwtProperties().getSecret());
    }

    /**
     * JWT 验证。使用国密 HmacSM3。
     */
    @Bean
    public HmacSm3JwsVerifier hmacSm3JwsVerifier() throws KeyLengthException {
        return new HmacSm3JwsVerifier(jwtProperties().getSecret());
    }


    /**
     * 目录重定向拦截器。支持访问
     */
    @Bean
    public DirectoryRedirectInterceptor directoryRedirectInterceptor() {
        return new DirectoryRedirectInterceptor(resourceLoader, props.isFileToDir(), props.isDirToFile());
    }
}
