package com.dascms.cms.core.service;

import com.dascms.cms.core.domain.user.UserLoginRecord;
import com.dascms.cms.core.mapper.UserLoginRecordMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class UserLoginRecordService {
    @Autowired
    private UserLoginRecordMapper userLoginRecordMapper;

    public void saveLoginRecord(Long userId) {
        UserLoginRecord userLoginRecord = new UserLoginRecord();
        userLoginRecord.setUserId(userId);
        userLoginRecord.setLoginTime(new Date());
        userLoginRecordMapper.insert(userLoginRecord);
    }

    /**
     * 获取用户上次登录时间
     */
    public Date getLastLoginTime(Long userId) {
        List<UserLoginRecord> userLoginRecords = userLoginRecordMapper.getByUserId(userId);
        if (CollectionUtils.isEmpty(userLoginRecords) || userLoginRecords.size() < 2) {
            return new Date();
        }
        return userLoginRecords.get(1).getLoginTime();
    }
}
