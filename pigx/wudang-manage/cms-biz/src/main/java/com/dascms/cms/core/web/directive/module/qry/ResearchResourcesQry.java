package com.dascms.cms.core.web.directive.module.qry;

import freemarker.template.TemplateModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 研究资源库查询参数封装
 */
@Data
public class ResearchResourcesQry extends BaseQry {
    //标签id
    private List<Long> tagIds;
    //语种
    private List<String> languages;
    //标题
    private String title;

    public ResearchResourcesQry() {
    }

    public ResearchResourcesQry(Map<String, TemplateModel> params) {
        initChannelIds(params);
    }

    public List<Long> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<Long> tagIds) {
        this.tagIds = tagIds;
    }

    public List<String> getLanguages() {
        return languages;
    }

    public void setLanguages(List<String> languages) {
        this.languages = languages;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
