package com.dascms.cms.core.web.api;

import com.dascms.cms.core.domain.Attachment;
import com.dascms.cms.core.domain.Config;
import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.service.AttachmentService;
import com.dascms.cms.core.service.ConfigService;
import com.dascms.cms.core.service.SiteService;
import com.dascms.cms.core.support.Contexts;
import com.dascms.cms.core.support.LoginUserUtil;
import com.dascms.cms.core.support.Props;
import com.dascms.cms.core.web.backendapi.AbstractUploadController;
import com.dascms.commons.file.FileHandler;
import com.dascms.commons.image.ImageHandler;
import com.dascms.commons.image.Images;
import com.dascms.commons.web.PathResolver;
import com.dascms.commons.web.Uploads;
import com.dascms.commons.web.exception.Http400Exception;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import ws.schild.jave.EncoderException;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.dascms.cms.core.support.UrlConstants.API;
import static com.dascms.cms.core.support.UrlConstants.FRONTEND_API;
import static com.dascms.commons.file.FilesEx.SLASH;
import static com.dascms.commons.web.Uploads.*;

/**
 * 上传 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "UploadController", description = "上传接口")
@RestController
@RequestMapping({API + "/upload", FRONTEND_API + "/upload"})
public class UploadController extends AbstractUploadController {
    private final ConfigService configService;
    private final SiteService siteService;

    public UploadController(AttachmentService attachmentService, ImageHandler imageHandler, PathResolver pathResolver,
                            ConfigService configService, SiteService siteService, Props props) {
        super(attachmentService, imageHandler, pathResolver, props);
        this.configService = configService;
        this.siteService = siteService;
    }

    @PostMapping("avatar-upload")
    //@PreAuthorize("isAuthenticated()")
    public Map<String, Object> avatarUpload(HttpServletRequest request) throws EncoderException, IOException {
        Config config = configService.getUnique();
        Site site = getDefaultSite(config.getDefaultSiteId());
        Contexts.setCurrentSite(site);
        Config.Upload upload = config.getUpload();
        return doUploadAvatar(request, upload.getImageLimitByte(), upload.getImageTypes(), AVATAR_TYPE, null);
    }

    private Map<String, Object> doUploadAvatar(HttpServletRequest request, long limitByte, String types, String type,
                                               @Nullable ExtraHandle extraHandle) throws IOException, EncoderException {
        MultipartFile multipart = getMultipart(request);
        Site site = Contexts.getCurrentSite();
        User user = LoginUserUtil.getLoginUser(request);
        // 检查文件大小
        long length = multipart.getSize();
        validateLimit(limitByte, length);
        // 检查文件后缀
        String extension = FilenameUtils.getExtension(multipart.getOriginalFilename());
        if (props.getUploadsExtensionExcludes().contains(StringUtils.lowerCase(extension))) {
            throw new Http400Exception(String.format("file extension not allowed: '%s'. blacklist: '%s'",
                    extension, props.getUploadsExtensionBlacklist()));
        }
        validateType(types, extension);

        FileHandler fileHandler = Contexts.getCurrentSite().getConfig().getUploadStorage().getFileHandler(pathResolver);
        File tempFile = Files.createTempFile("", "." + extension).toFile();
        String name = Optional.ofNullable(multipart.getOriginalFilename()).orElse("");
        try {
            Map<String, Object> result = new HashMap<>(8);
            multipart.transferTo(tempFile);
            if (IMAGE_TYPE.equalsIgnoreCase(type) || AVATAR_TYPE.equalsIgnoreCase(type)) {
                // 使用图片格式作为后缀。有时候支持透明格式的图片使用了错误的后缀(如png图片使用jpg后缀)，会导致透明部分变成黑色。
                extension = Images.getFormatName(tempFile);
                if (extension == null) {
                    throw new Http400Exception("Image format type not supported.");
                }
                File newFile = new File(FilenameUtils.removeExtension(tempFile.getName()) + "." + extension);
                if (tempFile.renameTo(newFile)) {
                    tempFile = newFile;
                }
            }
            // 获得存储路径和显示路径
            String pathname;
            if (AVATAR_TYPE.equalsIgnoreCase(type)) {
                pathname = SLASH + AVATAR_TYPE + SLASH + user.getId() + SLASH +
                        StringUtils.remove(UUID.randomUUID().toString(), '-') + "." + extension;
            } else {
                pathname = site.getBasePath(SLASH + type) + Uploads.getRandomFilename(extension);
            }
            String url = fileHandler.getDisplayPrefix() + pathname;
            // 执行额外的处理
            if (extraHandle != null) {
                String baseName = FilenameUtils.getBaseName(name);
                extraHandle.handle(tempFile, baseName, extension, pathname, fileHandler, site, user.getId(), result);
            }
            fileHandler.store(pathname, tempFile);
            attachmentService.insert(new Attachment(site.getId(), user.getId(), name, pathname, url, length));
            result.put("name", name);
            result.put("url", url);
            result.put("size", length);
            return result;
        } finally {
            if (tempFile.exists()) {
                FileUtils.deleteQuietly(tempFile);
            }
        }
    }

    @PostMapping("avatar-crop")
    //@PreAuthorize("isAuthenticated()")
    public Map<String, Object> avatarCrop(@RequestBody CropParams params, HttpServletRequest request) throws IOException {
        Config config = configService.getUnique();
        Site site = getDefaultSite(config.getDefaultSiteId());
        Contexts.setCurrentSite(site);
        return doAvatarCropNew(config, params, request);
    }

    protected Map<String, Object> doAvatarCropNew(Config config, CropParams params, HttpServletRequest request) throws IOException {
        Config.Register register = config.getRegister();
        User user = LoginUserUtil.getLoginUser(request);
        FileHandler fileHandler = config.getUploadStorage().getFileHandler(pathResolver);
        String src = fileHandler.getName(params.getUrl());
        String prefix = "/" + AVATAR_TYPE + "/" + user.getId() + "/";
        if (src == null || !src.startsWith(prefix)) {
            throw new Http400Exception("invalid avatar url: " + params.getUrl());
        }
        File file = fileHandler.getFile(src);
        if (file == null) {
            throw new Http400Exception("file not found: " + src);
        }
        String extension = FilenameUtils.getExtension(src);
        String name = src.substring(src.lastIndexOf("/") + 1);
        File tempFile = null;
        try {
            // 复制图片
            String filename = prefix + StringUtils.remove(UUID.randomUUID().toString(), '-') + "." + extension;
            String url = fileHandler.getDisplayPrefix() + filename;
            fileHandler.store(filename, file);
            attachmentService.insert(new Attachment(Contexts.getCurrentSiteId(), user.getId(),
                    name, filename, url, file.length()));
            // 图片裁剪。图片任意裁剪，生成新图片。
            String largeFilename = filename + Config.Register.AVATAR_LARGE + extension;
            String largeUrl = fileHandler.getDisplayPrefix() + largeFilename;
            tempFile = Files.createTempFile("", "." + extension).toFile();
            // 裁剪
            crop(fileHandler, file, tempFile, largeFilename, params);
            attachmentService.insert(new Attachment(Contexts.getCurrentSiteId(), user.getId(),
                    name, largeFilename, largeUrl, tempFile.length()));
            // 中头像
            String mediumFilename = filename + Config.Register.AVATAR_MEDIUM + extension;
            String mediumUrl = fileHandler.getDisplayPrefix() + mediumFilename;
            long mediumLength = thumbnail(imageHandler, fileHandler, tempFile, mediumFilename, extension,
                    register.getMediumAvatarSize(), register.getMediumAvatarSize());
            attachmentService.insert(new Attachment(Contexts.getCurrentSiteId(), user.getId(),
                    name, mediumFilename, mediumUrl, mediumLength));
            // 小头像
            String smallFilename = filename + Config.Register.AVATAR_SMALL + extension;
            String smallUrl = fileHandler.getDisplayPrefix() + smallFilename;
            long smallLength = thumbnail(imageHandler, fileHandler, tempFile, smallFilename, extension,
                    register.getSmallAvatarSize(), register.getSmallAvatarSize());
            attachmentService.insert(new Attachment(Contexts.getCurrentSiteId(), user.getId(),
                    name, smallFilename, smallUrl, smallLength));
            Map<String, Object> result = new HashMap<>(4);
            result.put("url", url);
            return result;
        } finally {
            if (file.exists()) {
                FileUtils.deleteQuietly(file);
            }
            if (tempFile != null && tempFile.exists()) {
                FileUtils.deleteQuietly(tempFile);
            }
        }
    }

    private Site getDefaultSite(Long defaultSiteId) {
        return Optional.ofNullable(siteService.select(defaultSiteId)).orElseThrow(() ->
                new IllegalStateException("default site not found. ID: " + defaultSiteId));
    }

}
