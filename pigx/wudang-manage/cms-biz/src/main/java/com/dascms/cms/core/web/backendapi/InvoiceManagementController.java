package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.domain.invoice.InvoiceManagement;
import com.dascms.cms.core.service.InvoiceManagementService;
import com.dascms.cms.core.service.args.InvoiceManagementArgs;
import com.dascms.cms.core.support.UrlConstants;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(UrlConstants.BACKEND_API + "/invoice")
public class InvoiceManagementController {

    @Autowired
    private InvoiceManagementService invoiceManagementService;

    /**
     * 发票列表
     */
    @RequestMapping("/page")
    @ResponseBody
    public ResponseEntity<Page<InvoiceManagement>> page(InvoiceManagementArgs invoiceManagementArgs) {
        return invoiceManagementService.page(invoiceManagementArgs);
    }

    /**
     * 修改备注
     */
    @RequestMapping("/updateRemark")
    @ResponseBody
    public ResponseEntity<String> updateRemark(@RequestBody InvoiceManagementArgs invoiceManagementArgs) {
        return invoiceManagementService.updateRemark(invoiceManagementArgs);
    }
}
