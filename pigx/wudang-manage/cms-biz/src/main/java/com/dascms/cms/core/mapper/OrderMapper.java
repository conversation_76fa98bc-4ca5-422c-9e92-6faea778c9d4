package com.dascms.cms.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.dashboard.StasticsItem;
import com.dascms.cms.core.domain.material.UjcmsMaterial;
import com.dascms.cms.core.domain.order.Order;
import com.dascms.cms.core.service.args.OrderArgs;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface OrderMapper extends BaseMapper<Order> {
    /**
     * 订单列表
     *
     * @param args
     * @return
     */
    List<Order> list(OrderArgs args);

    /**
     * 按照时间统计订单交易的素材数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Integer count(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计交易额
     *
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal countPrice(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计订单数量
     *
     * @param ratioStartTime
     * @param startTime
     * @return
     */
    Integer countOrder(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 订单产品类型统计
     *
     * @param args
     * @return
     */
    List<StasticsItem> orderProductType(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 按照产品分类统计交易额和数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<StasticsItem> saleRank(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计付款数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Integer countPay(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 成交趋势
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<StasticsItem> saleTrend(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 客户年龄分布
     *
     * @param startTime
     * @param endTime
     * @param birthStart
     * @param birthEnd
     * @return
     */
    Integer customerAge(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
                        @Param("birthStart") Date birthStart, @Param("birthEnd") Date birthEnd);

    /**
     * 客户性别分布
     */
    Integer customerSex(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("sex") int sex);

    /**
     * 客户类型分布
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Integer customerType(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
                         @Param("type") int type);

    /**
     * 查询订单详情
     *
     * @param orderNo
     * @return
     */
    Order selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据用户id查询订单数量
     *
     * @param id
     * @return
     */
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 根据用户id查询不同类别的订单数量
     *
     * @param userId
     * @return
     */
    Integer countByUserIdAndType(@Param("userId") Long userId, @Param("type") Integer type,
                                 @Param("orderStatus") int orderStatus);

    /**
     * 我已经付款的素材
     *
     * @param args
     * @return
     */
    List<UjcmsMaterial> mylist(OrderArgs args);

    /**
     * 根据用户id查询不同猪状态
     *
     * @param userId
     * @return
     */
    Integer countByUserIdAndStatus(@Param("userId") Long userId,
                                   @Param("orderStatus") int orderStatus);

    /**
     * 根据素材id查询订待支付的订单数量
     *
     * @param id
     * @return
     */
    Integer selectCountByMaterialId(@Param("id") Long id);
}
