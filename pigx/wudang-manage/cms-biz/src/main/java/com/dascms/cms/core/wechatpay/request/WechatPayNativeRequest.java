package com.dascms.cms.core.wechatpay.request;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信支付Native下单请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WechatPayNativeRequest {

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 商户号
     */
    private String mchid;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商户订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 交易结束时间
     */
    @JsonProperty("time_expire")
    private String timeExpire;

    /**
     * 附加数据
     */
    private String attach;

    /**
     * 通知地址
     */
    @JsonProperty("notify_url")
    private String notifyUrl;

    /**
     * 订单优惠标记
     */
    @JsonProperty("goods_tag")
    private String goodsTag;

    /**
     * 电子发票入口开放标识
     */
    @JsonProperty("support_fapiao")
    private Boolean supportFapiao;

    /**
     * 订单金额信息
     */
    private Amount amount;

    /**
     * 优惠功能
     */
    private Detail detail;

    /**
     * 场景信息
     */
    @JsonProperty("scene_info")
    private SceneInfo sceneInfo;

    /**
     * 结算信息
     */
    @JsonProperty("settle_info")
    private SettleInfo settleInfo;
}

/**
 * 订单金额信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
class Amount {

    /**
     * 总金额，单位为分
     */
    private Integer total;

    /**
     * 货币类型，CNY：人民币，境内商户号仅支持人民币
     */
    private String currency;
}

/**
 * 优惠功能
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
class Detail {

    /**
     * 订单原价，单位为分
     */
    @JsonProperty("cost_price")
    private Integer costPrice;

    /**
     * 发票ID
     */
    @JsonProperty("invoice_id")
    private String invoiceId;

    /**
     * 单品列表
     */
    @JsonProperty("goods_detail")
    private GoodsDetail[] goodsDetail;
}

/**
 * 单品信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
class GoodsDetail {

    /**
     * 商户侧商品编码
     */
    @JsonProperty("merchant_goods_id")
    private String merchantGoodsId;

    /**
     * 微信支付商品编码
     */
    @JsonProperty("wechatpay_goods_id")
    private String wechatpayGoodsId;

    /**
     * 商品名称
     */
    @JsonProperty("goods_name")
    private String goodsName;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品单价，单位为分
     */
    @JsonProperty("unit_price")
    private Integer unitPrice;
}

/**
 * 场景信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
class SceneInfo {

    /**
     * 用户终端IP
     */
    @JsonProperty("payer_client_ip")
    private String payerClientIp;

    /**
     * 商户端设备号
     */
    @JsonProperty("device_id")
    private String deviceId;

    /**
     * 商户门店信息
     */
    @JsonProperty("store_info")
    private StoreInfo storeInfo;
}

/**
 * 商户门店信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
class StoreInfo {

    /**
     * 门店编号
     */
    private String id;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 地区编码
     */
    @JsonProperty("area_code")
    private String areaCode;

    /**
     * 详细地址
     */
    private String address;
}

/**
 * 结算信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
class SettleInfo {

    /**
     * 是否指定分账
     */
    @JsonProperty("profit_sharing")
    private Boolean profitSharing;
}

