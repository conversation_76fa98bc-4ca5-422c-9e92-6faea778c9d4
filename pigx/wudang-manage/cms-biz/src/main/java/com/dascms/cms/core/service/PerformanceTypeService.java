package com.dascms.cms.core.service;

import com.github.pagehelper.page.PageMethod;
import com.dascms.cms.core.domain.PerformanceType;
import com.dascms.cms.core.domain.base.PerformanceTypeBase;
import com.dascms.cms.core.mapper.ChannelMapper;
import com.dascms.cms.core.mapper.PerformanceTypeMapper;
import com.dascms.cms.core.service.args.PerformanceTypeArgs;
import com.dascms.commons.db.identifier.SnowflakeSequence;
import com.dascms.commons.db.order.OrderEntityUtils;
import com.dascms.commons.query.QueryInfo;
import com.dascms.commons.query.QueryParser;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 绩效类型 Service
 *
 * <AUTHOR>
 */
@Service
public class PerformanceTypeService {
    private final ChannelMapper channelMapper;
    private final PerformanceTypeMapper mapper;
    private final SnowflakeSequence snowflakeSequence;

    public PerformanceTypeService(ChannelMapper channelMapper, PerformanceTypeMapper mapper,
                                  SnowflakeSequence snowflakeSequence) {
        this.channelMapper = channelMapper;
        this.mapper = mapper;
        this.snowflakeSequence = snowflakeSequence;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(PerformanceType bean) {
        bean.setId(snowflakeSequence.nextId());
        bean.setOrder(bean.getId());
        mapper.insert(bean);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(PerformanceType bean) {
        mapper.update(bean);
    }

    @Transactional(rollbackFor = Exception.class)
    public void moveOrder(Long fromId, Long toId) {
        OrderEntityUtils.move(mapper, fromId, toId);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 将栏目中引用该绩效类型的字段设置为null
        channelMapper.updatePerformanceTypeIdToNull(id);
        return mapper.delete(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(List<Long> ids) {
        return ids.stream().filter(Objects::nonNull).mapToInt(this::delete).sum();
    }

    @Nullable
    public PerformanceType select(Long id) {
        return mapper.select(id);
    }

    public List<PerformanceType> selectList(PerformanceTypeArgs args) {
        QueryInfo queryInfo = QueryParser.parse(args.getQueryMap(), PerformanceTypeBase.TABLE_NAME, "order,id");
        return mapper.selectAll(queryInfo);
    }

    public List<PerformanceType> selectList(PerformanceTypeArgs args, int offset, int limit) {
        return PageMethod.offsetPage(offset, limit, false).doSelectPage(() -> selectList(args));
    }

    public List<PerformanceType> listBySiteId(Long siteId) {
        return selectList(PerformanceTypeArgs.of().siteId(siteId));
    }
}