package com.dascms.cms;

import com.dascms.cms.core.runnable.HoKeyWordSearchRunnable;
import com.dascms.cms.core.support.Props;
import com.dascms.commons.image.ImageHandler;
import com.dascms.commons.image.ThumbnailatorHandler;
import com.dascms.commons.web.PathResolver;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.pig4cloud.pigx.common.core.util.SpringContextHolder;
import com.pig4cloud.pigx.common.feign.annotation.EnablePigxFeignClients;
import com.pig4cloud.pigx.common.security.annotation.EnablePigxResourceServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FullyQualifiedAnnotationBeanNameGenerator;
import org.springframework.lang.Nullable;
import org.springframework.mobile.device.LiteDeviceResolver;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.WebApplicationInitializer;

import javax.servlet.ServletContext;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@EnablePigxFeignClients
@EnablePigxResourceServer
@EnableDiscoveryClient
@SpringBootApplication(nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class,scanBasePackages = {"com.dascms","com.pig4cloud.pigx"})
@EnableTransactionManagement
@EnableAspectJAutoProxy
@MapperScan({ "com.dascms.cms.core.mapper","com.dascms.cms.ext.mapper", "com.pig4cloud.pigx.common.log.mapper" })
public class CmsBackendlication extends SpringBootServletInitializer implements WebApplicationInitializer, ApplicationContextAware , CommandLineRunner {
    
    private static final org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(CmsBackendlication.class);
    
    /**
     * UJCMS 配置
     */
    @Bean
    public Props props() {
        return new Props();
    }


    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        LocalDateTimeDeserializer dateTimeDeserializer = new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTimeSerializer dateTimeSerializer = new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        JavaTimeModule module = new JavaTimeModule();
        module.addDeserializer(LocalDateTime.class, dateTimeDeserializer);
        module.addSerializer(LocalDateTime.class, dateTimeSerializer);
        module.addSerializer(Long.class, ToStringSerializer.instance);

        objectMapper.registerModule(module);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    /**
     * 设备识别器，用于识别是否手机访问
     */
    @Bean
    public LiteDeviceResolver liteDeviceResolver() {
        return new LiteDeviceResolver();
    }

    /**
     * 真实路径获取组件
     */
    @Bean
    public PathResolver pathResolver(ObjectProvider<ServletContext> servletContextProvider) {
        return new PathResolver(servletContextProvider);
    }

    /**
     * 图片处理组件
     */
    @Bean
    public ImageHandler imageHandler() {
        return new ThumbnailatorHandler();
    }


    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return customizerBuilder(builder);
    }

    public static void main(String[] args) {
        customizerBuilder(new SpringApplicationBuilder()).run(args);
        LOGGER.info("cms启动成功.....");
    }

    private static SpringApplicationBuilder customizerBuilder(SpringApplicationBuilder builder) {
        //Utils.boot();
        return builder.sources(CmsBackendlication.class);
    }

    @Nullable
    public static ApplicationContext applicationContext;

    @Override
    @SuppressWarnings("java:S2696")
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        CmsBackendlication.applicationContext = applicationContext;
    }

    @Nullable
    public static ApplicationContext getApplicationContext() {
        return CmsBackendlication.applicationContext;
    }

    @Override
    public void run(String... args) throws Exception {
        //初始化关键字搜索线程
        ThreadPoolTaskExecutor threadPoolTaskExecutor = SpringContextHolder.getBean(ThreadPoolTaskExecutor.class);
        threadPoolTaskExecutor.execute(new HoKeyWordSearchRunnable());
        LOGGER.info("初始化关键字搜索线程完成");
    }
}
