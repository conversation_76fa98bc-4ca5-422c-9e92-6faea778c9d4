package com.dascms.cms.core.service.args;

import java.util.Date;

import com.dascms.cms.core.web.support.BasePage;
import com.pig4cloud.pigx.common.core.util.DateUtil;

import liquibase.pro.packaged.nu;
import lombok.Data;

@Data
public class DashboardArgs extends BasePage {
    // 时间范围类型0：最近7天，1：最近30天，2：最近一年 3：全部
    private Integer timeType = 0;

    // 环比的开始时间--根据startTime往前推算，环比结束时间直接用startTime
    private Date ratioStartTime;

    /**
     * 结束时间--取当前时间
     */
    public Date getEndTime() {
        if (timeType != 3) {
            return new Date();
        }
        return null;
    }

    /**
     * 开始时间--根据时间范围类型和getEndTime往前推算
     */
    public Date getStartTime() {
        switch (timeType) {
            case 0:
                return DateUtil.addByDay(getEndTime(), -7);
            case 1:
                return DateUtil.addByDay(getEndTime(), -30);
            case 2:
                return DateUtil.addByDay(getEndTime(), -365);
            case 3:
                return null;
            default:
                return null;
        }
    }

    /**
     * 环比开始时间--根据getStartTime往前推算
     */
    public Date getRatioStartTime() {
        switch (timeType) {
            case 0:
                return DateUtil.addByDay(getStartTime(), -7);
            case 1:
                return DateUtil.addByDay(getStartTime(), -30);
            case 2:
                return DateUtil.addByDay(getStartTime(), -365);
            case 3:
                return null;
            default:
                return null;
        }
    }
}
