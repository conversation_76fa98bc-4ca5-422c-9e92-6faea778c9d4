package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.support.UrlConstants;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.controller.request.archive.ArchiveQry;
import com.pig4cloud.pigx.data.center.controller.request.column.ColumnInfoQry;
import com.pig4cloud.pigx.data.center.controller.request.leamaterial.MaterialPageQuery;
import com.pig4cloud.pigx.data.center.controller.request.metadata.MetaDataQry;
import com.pig4cloud.pigx.data.center.entity.resource.ModelDataEntity;
import com.pig4cloud.pigx.data.center.remote.RemoteDataassetsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(UrlConstants.BACKEND_API + "/dataassets")
public class CmsDataassetsController {

    @Autowired
    private RemoteDataassetsService remoteDataassetsService;

    /**
     * 查询资源和档案列表
     */
    @RequestMapping("dataPage")
    @ResponseBody
    public R dataPage(MetaDataQry metaDataQry) {
        return remoteDataassetsService.dataPage(metaDataQry);
    }

    /**
     * 查询资源附件列表
     */
    @RequestMapping("resourcesAttachments")
    @ResponseBody
    public R resourcesAttachments(MaterialPageQuery materialPageQuery) {
        if (StringUtils.isBlank(materialPageQuery.getTabName())) {
            materialPageQuery.setTabName(materialPageQuery.getTableName());
        }
        return remoteDataassetsService.resourcesAttachments(materialPageQuery);
    }

    /**
     * 查询资源附件列表
     */
    @RequestMapping("archivesAttachments")
    @ResponseBody
    public R archivesAttachments(ArchiveQry archiveQry) {
        return remoteDataassetsService.archivesAttachments(archiveQry);
    }

    /**
     * 查询字段列表
     */
    @RequestMapping("listColumnInfo")
    @ResponseBody
    public R listColumnInfo(ColumnInfoQry columnInfoQry) {
        return remoteDataassetsService.listColumnInfo(columnInfoQry);
    }

    /**
     * 资源详情
     */
    @RequestMapping("resouceDetail")
    @ResponseBody
    public R resouceDetail(ModelDataEntity modelDataEntity) {
        return remoteDataassetsService.resouceDetail(modelDataEntity);
    }

    /**
     * 档案详情
     */
    @RequestMapping("archiveDetail")
    @ResponseBody
    public R archiveDetail(ArchiveQry archiveQry) {
        return remoteDataassetsService.archiveDetail(archiveQry);
    }

        /**
     * 查询资源附件列表
     */
    @RequestMapping("resourcesAttachmentsPage")
    @ResponseBody
    public R resourcesAttachmentsPage(MaterialPageQuery materialPageQuery) {
        if (StringUtils.isBlank(materialPageQuery.getTabName())) {
            materialPageQuery.setTabName(materialPageQuery.getTableName());
        }
        return remoteDataassetsService.resourcesAttachmentsPage(materialPageQuery);
    }
}
