package com.dascms.cms.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.material.UjcmsMateialAuthFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface UjcmsMateialAuthFileMapper extends BaseMapper<UjcmsMateialAuthFile> {
    /**
     * 根据素材id查询版权文件
     */
    List<UjcmsMateialAuthFile> selectByMaterialId(@Param("materialId")Long materialId);

    /**
     * 根据素材id查询版权文件
     */
    List<UjcmsMateialAuthFile> selectByBatchMaterialId(@Param("materialIds") List<Long> materialIds);

    /**
     * 删除版权文件数据
     *
     * @param materialId
     */
    void deleteByMaterialId(@Param("materialId")Long materialId);
}
