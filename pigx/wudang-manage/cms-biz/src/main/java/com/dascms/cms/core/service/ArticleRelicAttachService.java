package com.dascms.cms.core.service;

import com.dascms.cms.core.domain.ArticleRelicAttach;
import com.dascms.cms.core.mapper.ArticleRelicAttachMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文物附件
 */
@Service
public class ArticleRelicAttachService {
    @Autowired
    private ArticleRelicAttachMapper mapper;

    public List<ArticleRelicAttach> selectByArticleId(Long articleId) {
        return mapper.selectByArticleId(articleId);
    }
}
