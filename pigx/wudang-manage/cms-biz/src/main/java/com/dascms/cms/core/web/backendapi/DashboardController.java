package com.dascms.cms.core.web.backendapi;

import static com.dascms.cms.core.support.UrlConstants.*;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.dascms.cms.core.domain.dashboard.StasticsItem;
import com.dascms.cms.core.service.DashboardService;
import com.dascms.cms.core.service.args.DashboardArgs;

@Controller
@RequestMapping(BACKEND_API + "/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    /**
     * 第一排的总计面板
     */
    @RequestMapping("summary")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> summary(DashboardArgs args) {
        return dashboardService.summary(args);
    }

    /**
     * 订单对应产品类型统计
     */
    @RequestMapping("orderProductType")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> orderProductType(DashboardArgs args) {
        return dashboardService.orderProductType(args);
    }

    /**
     * 销售排行
     */
    @RequestMapping("saleRank")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> saleRank(DashboardArgs args) {
        return dashboardService.saleRank(args);
    }

    /**
     * 交易漏斗
     */
    @RequestMapping("funnel")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> funnel(DashboardArgs args) {
        return dashboardService.funnel(args);
    }

    /**
     * 成交趋势
     */
    @RequestMapping("saleTrend")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> saleTrend(DashboardArgs args) {
        return dashboardService.saleTrend(args);
    }

    /**
     * 客户年龄分布
     */
    @RequestMapping("customerAge")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> customerAge(DashboardArgs args) {
        return dashboardService.customerAge(args);
    }
    /**
     * 客户性别分布
     */
    @RequestMapping("customerSex")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> customerSex(DashboardArgs args) {
        return dashboardService.customerSex(args);
    }
    /**
     * 客户类型分布
     */
    @RequestMapping("customerType")
    @ResponseBody
    public ResponseEntity<List<StasticsItem>> customerType(DashboardArgs args) {
        return dashboardService.customerType(args);
    }
}
