package com.dascms.cms.core.service.args;

import com.dascms.cms.core.web.support.BasePage;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class InvoiceManagementArgs extends BasePage {
    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 发票类型1电子普通发票2电子专用发票
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 开票状态1待处理2已开票3已失效
     */
    private Integer invoiceStatus;

    /**
     * 申请开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTm;

    /**
     * 申请结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTm;

    /**
     * 发票id
     */
    private Integer id;

    /**
     * 备注
     */
    private String remark;
}
