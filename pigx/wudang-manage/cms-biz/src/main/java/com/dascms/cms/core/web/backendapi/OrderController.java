package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.domain.order.Order;
import com.dascms.cms.core.service.OrderService;
import com.dascms.cms.core.service.args.OrderArgs;
import com.dascms.cms.core.support.UrlConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(UrlConstants.BACKEND_API + "/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 订单列表
     */
    @RequestMapping("/page")
    @ResponseBody
    public ResponseEntity<Page<Order>> page(OrderArgs args) {
        return orderService.page(args);
    }

    /**
     * 订单详情
     */
    @RequestMapping("/detail")
    @ResponseBody
    public ResponseEntity<Order> detail(Integer id) {
        return orderService.detail(id);
    }

    /**
     * 更新订单备注
     */
    @RequestMapping("/updateRemark")
    @ResponseBody
    public ResponseEntity<String> updateRemark(@RequestBody OrderArgs args) {
        return orderService.updateRemark(args.getId(), args.getRemark());
    }

    /**
     * 关闭订单
     */
    @RequestMapping("/closeOrder")
    @ResponseBody
    public ResponseEntity<String> closeOrder(@RequestBody OrderArgs args) {
        return orderService.closeOrder(args);
    }

    /**
     * 订单支付回调
     */
    @RequestMapping("/payCallback")
    @ResponseBody
    public ResponseEntity<String> payCallback(OrderArgs args) {
        return orderService.payCallback(args);
    }
}
