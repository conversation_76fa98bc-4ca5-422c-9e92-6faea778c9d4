package com.dascms.cms.core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.invoice.InvoiceManagement;
import com.dascms.cms.core.domain.order.Order;
import com.dascms.cms.core.service.args.InvoiceManagementArgs;
import com.dascms.cms.core.service.args.OrderArgs;

/**
 * <p>
 * 发票管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
public interface InvoiceManagementMapper extends BaseMapper<InvoiceManagement> {

    /**
     * 发票管理列表
     *
     * @param invoiceManagementArgs
     * @return
     */
    List<InvoiceManagement> list(InvoiceManagementArgs invoiceManagementArgs);

    /**
     * 统计开票数量
     * 
     * @param userId
     * @return
     */
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 订单列表--发票维度
     * 
     * @param args
     * @return
     */
    List<Order> order(OrderArgs args);
}
