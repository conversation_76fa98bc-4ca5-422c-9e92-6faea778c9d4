package com.dascms.cms.core.mapper;

import com.dascms.cms.core.domain.SensitiveWord;
import com.dascms.cms.core.web.request.SensitiveWordQry;
import com.dascms.commons.query.QueryInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface SensitiveWordMapper {
    /**
     * 插入数据
     *
     * @param bean 实体对象
     * @return 插入条数
     */
    int insert(SensitiveWord bean);

    /**
     * 更新数据
     *
     * @param bean 实体对象
     * @return 更新条数
     */
    int update(SensitiveWord bean);

    /**
     * 删除数据
     *
     * @param id 主键ID
     * @return 删除条数
     */
    int delete(Long id);

    /**
     * 根据主键获取数据
     *
     * @param id 主键ID
     * @return 实体对象。没有找到数据，则返回 {@code null}
     */
    @Nullable
    SensitiveWord select(Long id);

    /**
     * 根据查询条件获取列表
     *
     * @param queryInfo 查询条件
     * @return 数据列表
     */
    List<SensitiveWord> selectAll(@Nullable @Param("queryInfo") QueryInfo queryInfo);

    /**
     * 分页查询敏感词
     *
     * @param sensitiveWordQry
     * @return
     */
    List<SensitiveWord> selectPage(SensitiveWordQry sensitiveWordQry);

    /**
     * 校验重复
     *
     * @param name
     * @return
     */
    Integer checkSame(@Param("name") String name, @Param("id") Long id);
}