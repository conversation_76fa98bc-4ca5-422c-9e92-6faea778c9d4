package com.dascms.cms.core.web.util;


import com.dascms.cms.core.support.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 图片水印工具类，提供添加和提取水印功能
 * 支持可见和不可见水印
 */
public class ImageWatermarkUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageWatermarkUtil.class);
    //---------------------------------------------------------------------
    // 不可见水印功能
    //---------------------------------------------------------------------

    /**
     * 添加不可见水印
     * <p>
     * /**
     * 水印数据类
     */
    public static class WatermarkData {
        private String id;      // 唯一标识
        private String owner;   // 版权所有者
        private long timestamp; // 添加水印的时间戳
        private String extra;   // 额外信息

        public WatermarkData() {
            this.id = UUID.randomUUID().toString();
            this.timestamp = System.currentTimeMillis();
        }

        public WatermarkData(String owner) {
            this();
            this.owner = owner;
        }

        public WatermarkData(String owner, String extra) {
            this(owner);
            this.extra = extra;
        }

        @Override
        public String toString() {
            return String.format("{\"id\":\"%s\",\"owner\":\"%s\",\"timestamp\":%d,\"extra\":\"%s\"}",
                    id, owner, timestamp, extra != null ? extra : "");
        }

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOwner() {
            return owner;
        }

        public void setOwner(String owner) {
            this.owner = owner;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getExtra() {
            return extra;
        }

        public void setExtra(String extra) {
            this.extra = extra;
        }
    }

    /**
     * 水印验证结果类
     */
    public static class VerificationResult {
        private boolean valid;
        private WatermarkData data;
        private String message;

        public VerificationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public VerificationResult(boolean valid, WatermarkData data, String message) {
            this.valid = valid;
            this.data = data;
            this.message = message;
        }

        // Getters
        public boolean isValid() {
            return valid;
        }

        public WatermarkData getData() {
            return data;
        }

        public String getMessage() {
            return message;
        }
    }

    //---------------------------------------------------------------------
    // 可见水印功能
    //---------------------------------------------------------------------

    /**
     * 添加文本可见水印
     *
     * @param originalImage 原始图像
     * @param watermarkText 水印文本
     * @param opacity       不透明度 (0.0-1.0)
     * @param fontSize      字体大小
     * @param color         水印颜色
     * @param position      位置 ("center", "bottom_right", "top_left", "tiled")
     * @return 添加了水印的图像
     */
    public static BufferedImage addVisibleTextWatermark(
            BufferedImage originalImage, String watermarkText, float opacity,
            int fontSize, Color color, String position) {
        // 创建图像副本
        BufferedImage watermarkedImage = new BufferedImage(
                originalImage.getWidth(), originalImage.getHeight(), BufferedImage.TYPE_INT_ARGB);
        // 获取Graphics2D对象
        Graphics2D g2d = (Graphics2D) watermarkedImage.getGraphics();
        // 绘制原始图像
        g2d.drawImage(originalImage, 0, 0, null);
        // 设置字体和抗锯齿
        Font font = new Font("Arial", Font.BOLD, fontSize);
        g2d.setFont(font);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        // 获取文本大小
        FontMetrics fontMetrics = g2d.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(watermarkText);
        int textHeight = fontMetrics.getHeight();
        // 设置水印颜色和透明度
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, opacity));
        g2d.setColor(color);
        // 根据位置绘制水印
        switch (position.toLowerCase()) {
            case "center":
                // 图像中心
                g2d.drawString(watermarkText,
                        (originalImage.getWidth() - textWidth) / 2,
                        (originalImage.getHeight() + textHeight) / 2);
                break;
            case "bottom_right":
                // 右下角
                g2d.drawString(watermarkText,
                        originalImage.getWidth() - textWidth - 10,
                        originalImage.getHeight() - 10);
                break;
            case "top_left":
                // 左上角
                g2d.drawString(watermarkText, 10, textHeight + 10);
                break;
            case "tiled":
                // 平铺整个图像
                // 旋转45度
                g2d.rotate(Math.toRadians(45), originalImage.getWidth() / 2, originalImage.getHeight() / 2);
                // 计算瓦片间距
                int xStep = textWidth + 40;
                int yStep = textHeight + 40;
                // 绘制平铺水印
                for (int x = -originalImage.getWidth(); x < originalImage.getWidth() * 2; x += xStep) {
                    for (int y = -originalImage.getHeight(); y < originalImage.getHeight() * 2; y += yStep) {
                        g2d.drawString(watermarkText, x, y);
                    }
                }
                break;
            default:
                // 默认为中心
                g2d.drawString(watermarkText,
                        (originalImage.getWidth() - textWidth) / 2,
                        (originalImage.getHeight() + textHeight) / 2);
        }
        g2d.dispose();
        return watermarkedImage;
    }

    /**
     * 将BufferedImage保存为图像文件
     *
     * @param image    图像
     * @param filePath 保存路径
     * @param format   图像格式 (jpg, png, etc.)
     * @throws IOException 如果保存失败
     */
    public static void saveHighQualityImage(BufferedImage image, String filePath, String format) throws IOException {
        File outputFile = new File(filePath);
        // 处理 JPG 透明度问题
        if ((format.equalsIgnoreCase("jpg") || format.equalsIgnoreCase("jpeg")) &&
                image.getTransparency() != BufferedImage.OPAQUE) {
            BufferedImage rgbImage = new BufferedImage(
                    image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = rgbImage.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
            g2d.drawImage(image, 0, 0, null);
            g2d.dispose();
            image = rgbImage;
        }
        // 使用 ImageWriter 设置高质量保存
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(format);
        if (!writers.hasNext()) {
            throw new IOException("没有可用的 " + format + " 图像写入器");
        }
        ImageWriter writer = writers.next();
        try (ImageOutputStream ios = ImageIO.createImageOutputStream(outputFile)) {
            writer.setOutput(ios);
            // 创建保存参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            // 对 JPEG 设置质量 (1.0 = 最高质量，几乎无损)
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.95f);  // 设置很高的质量，0.95 = 95%
            }
            // 写入图像
            writer.write(null, new IIOImage(image, null, null), param);
            LOGGER.info("高质量图像已保存到: " + outputFile.getAbsolutePath());
        } finally {
            writer.dispose();
        }
    }

    /**
     * 从文件加载图像
     *
     * @param filePath 图像文件路径
     * @return 加载的BufferedImage
     * @throws IOException 如果加载失败
     */
    public static BufferedImage loadImage(String filePath) throws IOException {
        return ImageIO.read(new File(filePath));
    }

    /**
     * 为图片添加可见文本水印并保存
     *
     * @param inputPath     输入图片路径
     * @param outputPath    输出图片路径
     * @param watermarkText 水印文本内容
     * @param opacity       水印不透明度 (0.0-1.0)
     * @param fontSize      水印字体大小
     * @param color         水印颜色
     * @param position      水印位置 ("center", "bottom_right", "top_left", "tiled")
     * @return 处理是否成功
     */
    public static boolean addWatermarkAndSave(String inputPath, String outputPath,
                                              String watermarkText, float opacity,
                                              int fontSize, Color color,
                                              String position) {
        try {
            // 1. 加载原始图像
            BufferedImage originalImage = loadImage(inputPath);
            if (originalImage == null) {
                LOGGER.error("无法加载图片: " + inputPath);
                return false;
            }
            // 2. 添加可见水印
            BufferedImage watermarkedImage = addVisibleTextWatermarkWithChineseSupport(
                    originalImage,
                    watermarkText,
                    opacity,
                    fontSize,
                    color,
                    position
            );
            // 3. 提取文件格式（从输出路径）
            String format = getFileExtension(outputPath);
            if (format == null || format.isEmpty()) {
                format = "jpg"; // 默认格式
            }
            // 4. 高质量保存图片
            saveHighQualityImage(watermarkedImage, outputPath, format);
            LOGGER.info("水印处理完成: " + outputPath);
            return true;
        } catch (Exception e) {
            LOGGER.error("添加水印过程中发生错误: ", e);
            e.printStackTrace();
            return false;
        }
    }

    public static boolean addWatermark(String inputPath, String outputPath,
                                              String watermarkText, float opacity,
                                              int fontSize, Color color,
                                              String position) {
        addWatermarkAndSave(inputPath, outputPath, watermarkText, opacity, fontSize, color, position);
        addWatermarkAndSave(inputPath, outputPath, "数字武当", opacity, fontSize, color, "all_corners");
        return true;
    }

    /**
     * 从文件路径中提取文件扩展名
     */
    private static String getFileExtension(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 添加支持中文的文本水印
     */
    /**
     * 添加中文文本水印到图像
     *
     * @param originalImage 原始图像
     * @param watermarkText 水印文本
     * @param opacity 不透明度（0.0-1.0）
     * @param fontSize 字体大小
     * @param color 水印颜色
     * @param position 水印位置："top_left", "top_right", "bottom_left", "bottom_right", "center", "all_corners", "tiled"
     * @return 添加水印后的图像
     */
    public static BufferedImage addVisibleTextWatermarkWithChineseSupport(
            BufferedImage originalImage, String watermarkText, float opacity,
            int fontSize, Color color, String position) {
        // 创建图像副本
        BufferedImage watermarkedImage = new BufferedImage(
                originalImage.getWidth(), originalImage.getHeight(), BufferedImage.TYPE_INT_ARGB);
        // 获取Graphics2D对象
        Graphics2D g2d = watermarkedImage.createGraphics();
        // 绘制原始图像
        g2d.drawImage(originalImage, 0, 0, null);
        // 尝试使用系统可用的几种支持中文的字体
        Font font = createCompatibleFont(fontSize);// 第一选择：宋体
        g2d.setFont(font);
        // 设置抗锯齿和高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        // 获取文本大小
        FontMetrics fontMetrics = g2d.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(watermarkText);
        int textHeight = fontMetrics.getHeight();
        // 设置水印颜色和透明度
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, opacity));
        g2d.setColor(color);
        // 设置边距
        int margin = 20;
        // 处理平铺模式
        if ("tiled".equalsIgnoreCase(position)) {
            // 旋转45度
            g2d.rotate(Math.toRadians(45), originalImage.getWidth() / 2, originalImage.getHeight() / 2);
            // 计算瓦片间距
            int xStep = textWidth + 100;
            int yStep = textHeight + 100;
            // 绘制平铺水印
            for (int tileX = -originalImage.getWidth(); tileX < originalImage.getWidth() * 2; tileX += xStep) {
                for (int tileY = -originalImage.getHeight(); tileY < originalImage.getHeight() * 2; tileY += yStep) {
                    g2d.drawString(watermarkText, tileX, tileY);
                }
            }
            g2d.dispose();
            return watermarkedImage;
        }
        // 处理全部角落模式
        if ("all_corners".equalsIgnoreCase(position) || "all_positions".equalsIgnoreCase(position)) {
            // 左上角
            g2d.drawString(watermarkText, margin, textHeight + margin - fontMetrics.getDescent());
            // 右上角
            g2d.drawString(watermarkText, originalImage.getWidth() - textWidth - margin,
                    textHeight + margin - fontMetrics.getDescent());
            // 左下角
            g2d.drawString(watermarkText, margin,
                    originalImage.getHeight() - margin - fontMetrics.getDescent());

            // 右下角--放文件id
//            g2d.drawString(watermarkText, originalImage.getWidth() - textWidth - margin,
//                    originalImage.getHeight() - margin - fontMetrics.getDescent());
            // 中间--放订单号
//            g2d.drawString(watermarkText, (originalImage.getWidth() - textWidth) / 2,
//                    (originalImage.getHeight() + textHeight) / 2 - fontMetrics.getDescent());

            g2d.dispose();
            return watermarkedImage;
        }
        // 处理单个位置的水印
        int x = 0, y = 0;
        switch (position.toLowerCase()) {
            case "center":
                x = (originalImage.getWidth() - textWidth) / 2;
                y = (originalImage.getHeight() + textHeight) / 2 - fontMetrics.getDescent();
                break;
            case "bottom_right":
                x = originalImage.getWidth() - textWidth - margin;
                y = originalImage.getHeight() - margin - fontMetrics.getDescent();
                break;
            case "bottom_left":
                x = margin;
                y = originalImage.getHeight() - margin - fontMetrics.getDescent();
                break;
            case "top_left":
                x = margin;
                y = textHeight + margin - fontMetrics.getDescent();
                break;
            case "top_right":
                x = originalImage.getWidth() - textWidth - margin;
                y = textHeight + margin - fontMetrics.getDescent();
                break;
            default:
                // 默认中心位置
                x = (originalImage.getWidth() - textWidth) / 2;
                y = (originalImage.getHeight() + textHeight) / 2 - fontMetrics.getDescent();
        }
        // 绘制水印文本
        g2d.drawString(watermarkText, x, y);
        // 释放资源
        g2d.dispose();
        return watermarkedImage;
    }

    /**
     * 创建兼容的字体，支持中文显示
     * @param fontSize 字体大小
     * @return 兼容的字体对象
     */
    private static Font createCompatibleFont(int fontSize) {
        // 按优先级尝试不同字体
        String[] fontNames = {
                "SimSun",           // 宋体（Windows）
                "Microsoft YaHei",  // 微软雅黑（Windows）
                "WenQuanYi Micro Hei", // 文泉驿微米黑（Linux）
                "Noto Sans CJK SC",    // Google Noto字体（Linux）
                "DejaVu Sans",         // DejaVu字体（Linux常见）
                "Liberation Sans",     // Liberation字体（Linux）
                Font.SANS_SERIF,       // 系统默认无衬线字体
                Font.SERIF,            // 系统默认衬线字体
                "Dialog"               // Java默认字体
        };

        // 获取系统可用字体
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] availableFonts = ge.getAvailableFontFamilyNames();
        Set<String> availableFontSet = new HashSet<>(Arrays.asList(availableFonts));

        // 按优先级选择可用字体
        for (String fontName : fontNames) {
            if (availableFontSet.contains(fontName)) {
                Font font = new Font(fontName, Font.BOLD, fontSize);
                // 验证字体是否能正确显示中文
                if (font.canDisplayUpTo("数字武当") == -1) {
                    LOGGER.info("使用字体: " + fontName);
                    return font;
                }
            }
        }

        // 如果都不可用，使用逻辑字体
        LOGGER.warn("未找到合适的中文字体，使用默认字体");
        return new Font(Font.SANS_SERIF, Font.BOLD, fontSize);
    }

    public static void main(String[] args) {
        String filePath="/usr/wds/static/uploads/1115/file/2025/07/1.png";
        String outputPath="/usr/wds/static/uploads/1115/file/2025/07/1_水印.png";
        addWatermarkAndSave(filePath,
                outputPath, "数字武当", 0.5f, 24, Color.WHITE,
                "tiled");
        System.err.println(FileUtils.getMd5(filePath));
        System.err.println(FileUtils.getMd5(outputPath));
    }
}
