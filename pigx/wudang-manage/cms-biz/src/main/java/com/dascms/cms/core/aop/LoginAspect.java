package com.dascms.cms.core.aop;


import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.support.LoginUserUtil;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 过滤器不生效，加上展示端不是所有的功能都要登录，所以加了这个aop
 */
@Aspect
@Component
public class LoginAspect {

    @Pointcut("@annotation(com.dascms.cms.core.aop.annotations.RequiresLogin)")
    public void login() {
    }

    @Before("login()")
    public void checkLogin() throws IOException {
        // 获取当前请求和响应对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return; // 非Web环境，直接返回
        }
        HttpServletRequest request = attributes.getRequest();
        HttpServletResponse response = attributes.getResponse();

        // 检查用户是否已登录（假设你有一个方法isLoggedIn()来检查）
        boolean isLoggedIn = isLoggedIn(request);
        if (!isLoggedIn) {
            // 如果未登录，重定向到登录页
            response.sendRedirect(LoginUserUtil.getLoginPage());
        }
    }

    private boolean isLoggedIn(HttpServletRequest request) {
        User currentUser = LoginUserUtil.getLoginUser(request);
        if (currentUser == null) {
            return false;
        }
        return true;
    }
}