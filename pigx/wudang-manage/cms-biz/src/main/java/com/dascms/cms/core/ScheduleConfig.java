package com.dascms.cms.core;

import com.dascms.cms.core.component.ViewCountService;
import com.dascms.cms.core.domain.Article;
import com.dascms.cms.core.domain.User;
import com.dascms.cms.core.generator.HtmlGenerator;
import com.dascms.cms.core.service.ArticleService;
import com.dascms.cms.core.service.ConfigService;
import com.dascms.cms.core.service.PayService;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 定时任务 配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableScheduling
public class ScheduleConfig {
    private final ViewCountService viewCountService;
    private final PayService payService;

    public ScheduleConfig(ViewCountService viewCountService, PayService payService) {
        this.viewCountService = viewCountService;
        this.payService = payService;
    }

    @Scheduled(cron = "#{new java.util.Random().nextInt(25) + 5} * * * * ?")
    public void flushViewCountTask() {
        viewCountService.flushSiteViews();
        viewCountService.flushChannelViews();
        viewCountService.flushArticleViews();
    }

    @Scheduled(cron = "*/30 * * * * ?")
    public void updateOrderStatus() {
        payService.updateAllPendingPaymentOrderStatus();
    }

    /**
     * 更新访问统计信息。定时时间要比写入时间晚一些，以免数据未写入完成就开始统计。
     * <p>
     * 需要集群。只要在一台机器上执行即可，不需要在多台机器同时运行。
     */
    @Bean("updateViewsStatTrigger")
    public CronTriggerFactoryBean updateViewsStatTrigger(
            @Qualifier("updateViewsStatJobDetail") JobDetail updateViewsStatJobDetail) {
        CronTriggerFactoryBean factoryBean = new CronTriggerFactoryBean();
        factoryBean.setJobDetail(updateViewsStatJobDetail);
        factoryBean.setCronExpression("45 * * * * ?");
        return factoryBean;
    }

    /**
     * 更新统计任务
     */
    @Bean("updateViewsStatJobDetail")
    public JobDetailFactoryBean updateViewsStatJobDetail() {
        final JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
        factoryBean.setJobClass(UpdateViewsStatJob.class);
        // 没有绑定触发器时，必须设置持久性为 true
        factoryBean.setDurability(true);
        return factoryBean;
    }

    /**
     * 更新访问统计任务类
     */
    @Component
    public static class UpdateViewsStatJob extends QuartzJobBean {
        @Nullable
        private ViewCountService viewCountService;

        @Autowired
        public void setViewCountService(ViewCountService viewCountService) {
            this.viewCountService = viewCountService;
        }

        @Override
        protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
            Assert.notNull(viewCountService, "ViewCountService must not be null");
            viewCountService.updateViewsStat();
        }
    }

    /**
     * 更新统计任务
     */
    @Bean("updateArticleStatusJobDetail")
    public JobDetailFactoryBean updateArticleStatusJobDetail() {
        final JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
        factoryBean.setJobClass(UpdateArticleStatusJob.class);
        // 没有绑定触发器时，必须设置持久性为 true
        factoryBean.setDurability(true);
        return factoryBean;
    }

    /**
     * 更新文章状态。包括文章置顶和文章上下线。每10分钟执行一次。
     * <p>
     * 需要集群。只要在一台机器上执行即可，不需要在多台机器同时运行。
     */
    @Bean("updateArticleStatusTrigger")
    public CronTriggerFactoryBean updateArticleStatusTrigger(
            @Qualifier("updateArticleStatusJobDetail") JobDetail updateArticleStatusJobDetail) {
        CronTriggerFactoryBean factoryBean = new CronTriggerFactoryBean();
        factoryBean.setJobDetail(updateArticleStatusJobDetail);
        factoryBean.setCronExpression("0 0/10 * * * ?");
        return factoryBean;
    }

    @Component
    public static class UpdateArticleStatusJob extends QuartzJobBean {
        private final ArticleService articleService;
        private final HtmlGenerator htmlGenerator;
        private final ConfigService configService;

        public UpdateArticleStatusJob(ArticleService articleService, HtmlGenerator htmlGenerator,
                                      ConfigService configService) {
            this.articleService = articleService;
            this.htmlGenerator = htmlGenerator;
            this.configService = configService;
        }

        @Override
        protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
            List<Article> stickyArticles = articleService.listAndUpdateStickyDate();
            List<Article> onlineArticles = articleService.listAndUpdateOnlineStatus();
            List<Article> offlineArticles = articleService.listAndUpdateOfflineStatus();
            List<Article> articles = Stream.of(stickyArticles, onlineArticles, offlineArticles)
                    .flatMap(Collection::stream).collect(Collectors.toList());
            for (Article article : articles) {
                article.adjustStatus();
                articleService.update(article);
            }
            Long siteId = configService.getUnique().getDefaultSiteId();
            String taskName = "task.html.articleRelated";
            htmlGenerator.updateArticleRelatedHtml(siteId, User.ANONYMOUS_ID, taskName, articles, null);
        }
    }
}
