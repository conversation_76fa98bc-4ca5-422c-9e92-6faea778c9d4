package com.dascms.cms.core.web.directive;

import com.dascms.cms.core.domain.Dict;
import com.dascms.cms.core.domain.count.ChannelCount;
import com.dascms.cms.core.service.DictService;
import com.dascms.cms.core.service.args.DictArgs;
import com.dascms.cms.core.web.support.Directives;
import com.dascms.commons.freemarker.Freemarkers;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import freemarker.core.Environment;
import freemarker.template.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 字典列表 标签
 *
 * <AUTHOR>
 */
public class DictListDirective implements TemplateDirectiveModel {
    /**
     * 类型ID。整型（Integer）。
     */
    private static final String TYPE_ID = "typeId";
    /**
     * 类型别名。字符串（String）。
     */
    private static final String TYPE = "type";
    /**
     * 是否启用。布尔型（Boolean）。可选值：all(全部), false(禁用)。默认值：启用。
     */
    private static final String IS_ENABLED = "isEnabled";

    public static List<Dict> query(Map<String, ?> params, DictService dictService) {
        DictArgs args = DictArgs.of();

        Long typeId = Directives.getLong(params, TYPE_ID);
        String typeAlias = Directives.getString(params, TYPE);
        if (typeId != null) {
            args.typeId(typeId);
        } else if (StringUtils.isNotBlank(typeAlias)) {
            args.typeAlias(typeAlias);
        } else {
            throw new IllegalArgumentException("Params typeId or typeAlias is required.");
        }

        Optional.ofNullable(Directives.getBooleanDefault(params, IS_ENABLED, true)).ifPresent(args::enabled);

        Directives.handleOrderBy(args.getQueryMap(), params);
        int offset = Directives.getOffset(params);
        int limit = Directives.getLimit(params);

        return dictService.selectList(args, offset, limit);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void execute(Environment env, Map params, TemplateModel[] loopVars, TemplateDirectiveBody body)
            throws TemplateException, IOException {
        Freemarkers.requireLoopVars(loopVars);
        Freemarkers.requireBody(body);

        List<Dict> list = query(params, dictService);
        Object jsonKey = params.get("jsonKey");
        SimpleNumber channelId = (SimpleNumber) params.get("channelId");
        if (channelId != null) {
            List<Long> channelIds = ListBuilder.<Long>ofList().add(Long.valueOf(channelId.toString())).build();
            if (jsonKey != null) {
                dictArticleCount(list, channelIds, jsonKey.toString());
            }
        }
        loopVars[0] = env.getObjectWrapper().wrap(list);
        body.render(env.getOut());
    }

    /**
     * 统计文章数量
     */
    /**
     * 统计tag下面的文章数量
     *
     * @throws TemplateException
     * @throws IOException
     */
    private void dictArticleCount(List<Dict> dicts, List<Long> channelIds, String jsonKey) {
        if (CollectionUtils.isNotEmpty(dicts)) {
            List<String> tagNames = dicts.parallelStream().map(Dict::getName).collect(Collectors.toList());
            List<ChannelCount> channelCounts = dictService.dictArticleCount(tagNames, channelIds, jsonKey);
            if (CollectionUtils.isNotEmpty(channelCounts)) {
                Map<String, Integer> countMap = channelCounts.parallelStream().collect(Collectors.toMap(ChannelCount::getName, ChannelCount::getCount));
                for (Dict dict : dicts) {
                    dict.setArticleCount(countMap.get(dict.getName()));
                }
            }
        }
    }


    private final DictService dictService;

    public DictListDirective(DictService dictService) {
        this.dictService = dictService;
    }
}
