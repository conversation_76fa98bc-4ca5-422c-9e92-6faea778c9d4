package com.dascms.cms.core.service;

import com.dascms.cms.core.domain.material.UjcmsMaterial;
import com.dascms.cms.core.domain.material.UjcmsMaterialPrice;
import com.dascms.cms.core.domain.material.UserMaterialBuy;
import com.dascms.cms.core.domain.order.Order;
import com.dascms.cms.core.enums.PayMethodEnum;
import com.dascms.cms.core.mapper.OrderMapper;
import com.dascms.cms.core.mapper.UjcmsMaterialMapper;
import com.dascms.cms.core.mapper.UjcmsMaterialPriceMapper;
import com.dascms.cms.core.mapper.UserMaterialBuyMapper;
import com.dascms.cms.core.service.args.OrderArgs;
import com.dascms.cms.core.support.OrderNumberGenerator;
import com.dascms.commons.db.MyBatis;
import com.github.pagehelper.page.PageMethod;
import com.pig4cloud.pigx.common.core.constant.CommonConstants;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class OrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderService.class);

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UjcmsMaterialMapper ujcmsMaterialMapper;

    @Autowired
    private UjcmsMaterialPriceMapper ujcmsMaterialPriceMapper;

    @Autowired
    private UserMaterialBuyMapper userMaterialBuyMapper;

    public static final String PATH_REPLACE = "/1/";

    public ResponseEntity<Page<Order>> page(OrderArgs args) {
        // 返回包含分页信息和内容的 ResponseEntity
        return ResponseEntity.ok(MyBatis.springPage(PageMethod.startPage(args.getPageNum(), args.getPageSize())
                .doSelectPage(() -> list(args))));
    }

    private List<Order> list(OrderArgs args) {
        List<Order> list = orderMapper.list(args);
        //填充素材信息
        if (list != null && list.size() > 0) {
            List<Long> materialIds = list.parallelStream().map(Order::getProductId).distinct().collect(Collectors.toList());
            if (materialIds != null && materialIds.size() > 0) {
                List<UjcmsMaterial> ujcmsMaterials = ujcmsMaterialMapper.selectBatchIds(materialIds);
                if (ujcmsMaterials != null && ujcmsMaterials.size() > 0) {
                    Map<Long, UjcmsMaterial> collect = ujcmsMaterials.parallelStream().collect(Collectors.toMap(UjcmsMaterial::getId, ujcmsMaterial -> ujcmsMaterial, (oldValue, newValue) -> oldValue));
                    for (Order order : list) {
                        UjcmsMaterial ujcmsMaterial = collect.get(order.getProductId());
                        if (ujcmsMaterial != null) {
                            order.setMaterial(ujcmsMaterial);
                        }
                    }
                }
            }
            List<String> orderNos = list.parallelStream().map(Order::getOrderNo).distinct().collect(Collectors.toList());
            if (orderNos != null && orderNos.size() > 0) {
                List<UserMaterialBuy> userMaterialBuys = userMaterialBuyMapper.listByOrderNos(orderNos);
                if (userMaterialBuys != null && userMaterialBuys.size() > 0) {
                    Map<String, UserMaterialBuy> collect = userMaterialBuys.parallelStream().collect(Collectors.toMap(UserMaterialBuy::getOrderNo, userMaterialBuy -> userMaterialBuy, (oldValue, newValue) -> oldValue));
                    for (Order order : list) {
                        UserMaterialBuy userMaterialBuy = collect.get(order.getOrderNo());
                        if (userMaterialBuy != null) {
                            order.setUserMaterialBuy(userMaterialBuy);
                        }
                    }
                }
            }
        }
        return list;
    }

    public ResponseEntity<Order> detail(Integer id) {
        Order order = orderMapper.selectById(id);
        if (order != null) {
            UjcmsMaterial ujcmsMaterial = ujcmsMaterialMapper.selectById(order.getProductId());
            order.setMaterial(ujcmsMaterial);
        }
        return ResponseEntity.ok(order);
    }

    public ResponseEntity<String> updateRemark(Integer id, String remark) {
        Order order = orderMapper.selectById(id);
        if (order != null) {
            order.setRemark(remark);
            orderMapper.updateById(order);
        }
        return ResponseEntity.ok(CommonConstants.SUCCESS_MSG);
    }

    public ResponseEntity<String> closeOrder(OrderArgs args) {
        Order order = orderMapper.selectById(args.getId());
        if (order != null) {
            order.setCloseRemark(args.getCloseRemark());
            order.setOrderStatus((byte) 2);
            order.setCloseTime(new Date());
            order.setCloseUser(SecurityUtils.getUser().getUsername());
            orderMapper.updateById(order);
        }
        return ResponseEntity.ok(CommonConstants.SUCCESS_MSG);
    }

    public ResponseEntity<Order> create(Order order) {
        //生成订单号
        String orderNo = OrderNumberGenerator.generate();
        order.setOrderNo(orderNo);
        order.setCreateTime(new Date());
        order.setOrderStatus((byte) 0);
        order.setPaymentMethod(PayMethodEnum.WECHAT_PAY.getName());
        orderMapper.insert(order);
        return ResponseEntity.ok(order);
    }

    public ResponseEntity<String> cancel(Order order) {
        Order dbOrder = orderMapper.selectByOrderNo(order.getOrderNo());
        if (dbOrder != null) {
            if (!order.getBuyerId().equals(dbOrder.getBuyerId())) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("订单不属于当前用户");
            }
            dbOrder.setOrderStatus((byte) 2);
            dbOrder.setCancelReason(order.getCancelReason());
            orderMapper.updateById(dbOrder);
        }
        return ResponseEntity.ok(CommonConstants.SUCCESS_MSG);
    }

    public ResponseEntity<String> delete(Order order) {
        Order dbOrder = orderMapper.selectByOrderNo(order.getOrderNo());
        if (dbOrder != null) {
            if (!order.getBuyerId().equals(dbOrder.getBuyerId())) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("订单不属于当前用户");
            }
            orderMapper.deleteById(dbOrder.getId());
        }
        return ResponseEntity.ok(CommonConstants.SUCCESS_MSG);
    }

    public ResponseEntity<String> payCallback(OrderArgs args) {
        Order dbOrder = orderMapper.selectByOrderNo(args.getOrderNo());
        if (dbOrder != null) {
            if (Byte.valueOf((byte) 1).equals(dbOrder.getOrderStatus())) {//重复回调
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("订单已经支付");
            }
            dbOrder.setPayTime(new Date());
            dbOrder.setPayDate(new Date());
            dbOrder.setOrderStatus((byte) 1);
            orderMapper.updateById(dbOrder);
            //素材
            UjcmsMaterial ujcmsMaterial = ujcmsMaterialMapper.selectById(dbOrder.getProductId());
            //订单选中的规格
            UjcmsMaterialPrice byMaterialIdAndNorm = ujcmsMaterialPriceMapper.getByMaterialIdAndNorm(dbOrder.getProductId(), dbOrder.getProductSpec());
            if (byMaterialIdAndNorm != null) {
                //文件拷贝一份到用户目录下，同时更新文件路径和访问url
                String filePath = byMaterialIdAndNorm.getFilePath();
                String destFilePath = filePath.replace(PATH_REPLACE, CommonConstants.PATH_SEPARATOR + dbOrder.getOrderNo() + CommonConstants.PATH_SEPARATOR);
                String url = byMaterialIdAndNorm.getUrl();
                String destUrl = url.replace(PATH_REPLACE, CommonConstants.PATH_SEPARATOR + dbOrder.getOrderNo() + CommonConstants.PATH_SEPARATOR);
                UserMaterialBuy userMaterialBuy = CommonBuilder.of(UserMaterialBuy.class)
                        .with(UserMaterialBuy::setMaterialId, dbOrder.getProductId())
                        .with(UserMaterialBuy::setUserId, dbOrder.getBuyerId())
                        .with(UserMaterialBuy::setFilePath, destFilePath)
                        .with(UserMaterialBuy::setUrl, destUrl)
                        .with(UserMaterialBuy::setFormat, ujcmsMaterial.getFormat())
                        .with(UserMaterialBuy::setNorm, byMaterialIdAndNorm.getNorm())
                        .with(UserMaterialBuy::setNormParams, byMaterialIdAndNorm.getNormParams())
                        .with(UserMaterialBuy::setAuthor, ujcmsMaterial.getAuthor())
                        .with(UserMaterialBuy::setLicenseType, dbOrder.getLicenseType())
                        .with(UserMaterialBuy::setCatalogNo, ujcmsMaterial.getCatalogNo())
                        .with(UserMaterialBuy::setLogo, ujcmsMaterial.getLogo())
                        .with(UserMaterialBuy::setOrderNo, dbOrder.getOrderNo())
                        .with(UserMaterialBuy::setName, ujcmsMaterial.getName())
                        .build();
                userMaterialBuyMapper.insert(userMaterialBuy);
                //文件拷贝
                try {
                    FileUtils.copyFile(new File(filePath), new File(destFilePath));
                } catch (IOException e) {
                    LOGGER.error("文件拷贝失败", e);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("文件拷贝失败");
                }
            }
        }
        return ResponseEntity.ok(CommonConstants.SUCCESS_MSG);
    }
}
