package com.dascms.cms.core.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.Buckets;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.WildcardQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dascms.cms.core.domain.Site;
import com.dascms.cms.core.domain.catalog.Catalog;
import com.dascms.cms.core.domain.dashboard.StasticsItem;
import com.dascms.cms.core.domain.material.*;
import com.dascms.cms.core.domain.order.Order;
import com.dascms.cms.core.enums.MaterialEnum;
import com.dascms.cms.core.mapper.*;
import com.dascms.cms.core.queues.CmsQueues;
import com.dascms.cms.core.service.args.UjcmsMaterialArgs;
import com.dascms.cms.core.support.FfmpegUtil;
import com.dascms.cms.core.support.FileUtils;
import com.dascms.cms.core.web.util.ImageWatermarkUtil;
import com.dascms.cms.ext.mapper.CatalogMapper;
import com.dascms.commons.constants.OperationConstants;
import com.dascms.commons.db.MyBatis;
import com.dascms.commons.web.Uploads;
import com.github.pagehelper.page.PageMethod;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.common.core.util.ImageCompressUtil;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import com.pig4cloud.pigx.common.es.util.CommonEs8ClientUtil;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.data.center.remote.RemoteCataloService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.dascms.commons.file.FilesEx.SLASH;

@Service
@Transactional
public class UjcmsMaterialService {

    //批量导入素材任务缓存key前缀
    public static final String BATCH_IMPORT_MATERIAL_TASK_CACHE_KEY_PREFIX = "batch_import_material_task:";

    //任务id后缀，防止前端丢失进度
    public static final String TASK_ID_PREFIX = "task_";

    private static final Logger LOGGER = LoggerFactory.getLogger(UjcmsMaterialService.class);

    // 超高质量
    private static final String HIGH_QUALITY = "超高质量";
    //预览后缀
    private static final String PREVIEW_SUFFIX = "_preview.";
    //预览文件夹前缀
    private static final String PREVIEW_FOLDER_PREFIX = "/1/preview/";

    //第一帧后缀
    private static final String FIRST_FRAME_SUFFIX = "_first.";

    //视频截取第一帧作为logo默认格式
    private static final String DEFAULT_VIDEO_FRAME_FORMAT = "jpg";

    // 分辨率连接符号
    private static final String RESOLUTION_CONNECTOR = "x";

    @Value("${cms.upload.path:/usr/wds/static/uploads}")
    private String uploadPath;

    @Value("${cms.upload.url:/cms/front/static/uploads}")
    private String url;

    @Autowired
    private UjcmsMaterialMapper ujcmsMaterialMapper;

    @Autowired
    private UjcmsPricingStrategyRuleMapper ujcmsPricingStrategyRuleMapper;

    @Autowired
    private UjcmsPricingStrategyMapper ujcmsPricingStrategyMapper;

    @Autowired
    private CommonEs8ClientUtil commonEs8ClientUtil;

    @Value("${elasticsearch.rest.material:cms_material}")
    private String indexName;

    @Autowired
    private RemoteCataloService remoteCataloService;

    @Autowired
    private CatalogMapper catalogMapper;

    @Autowired
    private UjcmsMateialAuthFileMapper ujcmsMateialAuthFileMapper;

    @Autowired
    private HotKeywordSearchMapper hotKeywordSearchMapper;

    @Autowired
    private UserMaterialCollectMapper userMaterialCollectMapper;

    @Autowired
    private UserMaterialDownloadMapper userMaterialDownloadMapper;

    @Autowired
    private UjcmsMaterialPriceMapper ujcmsMaterialPriceMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private SiteMapper siteMapper;

    @Autowired
    private FfmpegUtil ffmpegUtil;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private UserMaterialBuyMapper userMaterialBuyMapper;

    /**
     * 默认质量标准
     */
    List<UjcmsPricingStrategyRule> defaultPricingStrategyRules = ListBuilder.<UjcmsPricingStrategyRule>ofList()
            .add(CommonBuilder.of(UjcmsPricingStrategyRule.class)
                    .with(UjcmsPricingStrategyRule::setNorm, "普通质量")
                    .with(UjcmsPricingStrategyRule::setNormParams, "1920x1080")
                    .with(UjcmsPricingStrategyRule::setNo, 1)
                    .build())
            .add(CommonBuilder.of(UjcmsPricingStrategyRule.class)
                    .with(UjcmsPricingStrategyRule::setNorm, "中等质量")
                    .with(UjcmsPricingStrategyRule::setNormParams, "2560x1440")
                    .with(UjcmsPricingStrategyRule::setNo, 2)
                    .build())
            .add(CommonBuilder.of(UjcmsPricingStrategyRule.class)
                    .with(UjcmsPricingStrategyRule::setNorm, "高质量")
                    .with(UjcmsPricingStrategyRule::setNormParams, "3840x2160")
                    .with(UjcmsPricingStrategyRule::setNo, 3)
                    .build())
            .add(CommonBuilder.of(UjcmsPricingStrategyRule.class)
                    .with(UjcmsPricingStrategyRule::setNorm, HIGH_QUALITY)
                    .with(UjcmsPricingStrategyRule::setNo, 4)
                    .build())
            .build();

    /**
     * 根据图片原始分辨率判断向下压缩
     * pricingStrategyRules里面存储的规格参数假设是已经排好序的，例如：1024x768 或 1280x720,2048x1080 或
     * 2560x1440，如果图片是2048x1080 或 2560x1440，则向1024x768 或 1280x720压缩
     *
     * @return
     */
    /**
     * 根据图片原始分辨率判断压缩或放大
     * 如果原始分辨率小于1920x1080，则放大到1920x1080
     * 如果原始分辨率在两个标准之间，则向下压缩到较小的标准
     *
     * @return
     */
    private void compressImage(String filePath, UjcmsMaterial ujcmsMaterial,
                               ImageMatchResult stringIntegerIntegerTriple) {
        if (stringIntegerIntegerTriple == null) {
            return;
        }
        // 原始分辨率
        int originalWidth = stringIntegerIntegerTriple.getSrcWidth();
        int originalHeight = stringIntegerIntegerTriple.getSrcHeight();
        double originalRatio = (double) originalWidth / originalHeight;
        ujcmsMaterial.setWidth(originalWidth);
        ujcmsMaterial.setHeight(originalHeight);
        // 最高质量的目标分辨率
        int targetWidth = stringIntegerIntegerTriple.getWidth();
        int targetHeight = stringIntegerIntegerTriple.getHeight();
        // 没有找到匹配的规格参数，不压缩
        if (targetWidth == 0 || targetHeight == 0) {
            return;
        }
        // 如果当前序号是4，那么需要把按照序号小于等于4的规格全部压缩，同理，如果当前序号是3，那么需要把按照序号小于等于3的规格全部压缩
        List<UjcmsPricingStrategyRule> pricingStrategyRules = ujcmsPricingStrategyRuleMapper
                .selectRulesByPricingStrategyId(
                        ujcmsMaterial.getPricingStrategyId());
        // 过滤出来序号小于等于当前序号的规格
        List<UjcmsPricingStrategyRule> filteredPricingStrategyRules = pricingStrategyRules.stream()
                .filter(rule -> rule.getNo() <= stringIntegerIntegerTriple.getNo())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredPricingStrategyRules)) {// 说明原始分辨率比最小分辨率还小，直接使用原始分辨率
            List<UjcmsPricingStrategyRule> collect = pricingStrategyRules.parallelStream()
                    .filter(rule -> rule.getNo() == 1).collect(Collectors.toList());
            for (UjcmsPricingStrategyRule ujcmsPricingStrategyRule : collect) {
                ujcmsPricingStrategyRule.setNormParams(originalWidth + RESOLUTION_CONNECTOR + originalHeight);
                filteredPricingStrategyRules.add(ujcmsPricingStrategyRule);
            }
        }
        // 压缩
        String srcFilePath = uploadPath + filePath;
        // 再压缩其他规格
        for (UjcmsPricingStrategyRule filteredPricingStrategyRules2 : filteredPricingStrategyRules) {
            String fileName = filePath.replace(".", "_" + filteredPricingStrategyRules2.getNorm() + ".");
            String destFilePath = uploadPath + fileName;
            // 重新获取目标分辨率
            if (StringUtils.isBlank(filteredPricingStrategyRules2.getNormParams())) {// 说明是超高质量，直接使用原始分辨率
                filteredPricingStrategyRules2.setNormParams(originalWidth + RESOLUTION_CONNECTOR + originalHeight);
            }
            String[] split = filteredPricingStrategyRules2.getNormParams().split(RESOLUTION_CONNECTOR);
            int width = Integer.parseInt(split[0]);
            int height = Integer.parseInt(split[1]);
            //自身所属规则不压缩，直接加水印保存
            if (filteredPricingStrategyRules2.getNo() == stringIntegerIntegerTriple.getNo()) {
                width = originalWidth;
                height = originalHeight;
            } else {
                if (width <= originalWidth) {//以宽度为基准压缩
                    height = (int) (width / originalRatio);
                } else if (height <= originalHeight) {//以高度为基准压缩
                    width = (int) (height / originalRatio);
                }
            }
            ImageCompressUtil.resizeImage(srcFilePath, destFilePath, width, height, false);
            // 加水印
            ImageWatermarkUtil.addWatermark(destFilePath, destFilePath, ujcmsMaterial.getId().toString(), 0.0f,
                    24, Color.WHITE, "bottom_right");
            // 保存素材可选规格
            UjcmsMaterialPrice ujcmsMaterialPrice = CommonBuilder.of(UjcmsMaterialPrice.class)
                    .with(UjcmsMaterialPrice::setMaterialId, ujcmsMaterial.getId())
                    .with(UjcmsMaterialPrice::setNorm, filteredPricingStrategyRules2.getNorm())
                    .with(UjcmsMaterialPrice::setNormParams, filteredPricingStrategyRules2.getNormParams())
                    .with(UjcmsMaterialPrice::setPrice, filteredPricingStrategyRules2.getPrice())
                    .with(UjcmsMaterialPrice::setPurpose, filteredPricingStrategyRules2.getOneUsedTo())
                    .with(UjcmsMaterialPrice::setFilePath, destFilePath)
                    .with(UjcmsMaterialPrice::setUrl, url + fileName)
                    .with(UjcmsMaterialPrice::setMd5, FileUtils.getMd5(destFilePath))
                    .build();
            ujcmsMaterialPriceMapper.insert(ujcmsMaterialPrice);
        }
    }

    /**
     * 匹配图片的规格
     * 如果原始分辨率小于最小标准(1920x1080)，则放大到最小标准
     * 如果原始分辨率在两个标准之间，则向下压缩到较小的标准
     *
     * @return
     */
    private ImageMatchResult matchImage(String filePath,
                                        List<UjcmsPricingStrategyRule> pricingStrategyRules) {
        Pair<Integer, Integer> dimensions = ImageCompressUtil.read(uploadPath + filePath);
        if (dimensions == null) {
            return new ImageMatchResult("", 0, 0, 0, 0, 0);
        }
        int originalWidth = dimensions.getLeft();
        int originalHeight = dimensions.getRight();
        // 按照分辨率从小到大排序规格参数
        List<ImageMatchResult> sortedNorms = new ArrayList<>();
        for (UjcmsPricingStrategyRule rule : pricingStrategyRules) {
            String normName = rule.getNorm();
            Integer no = rule.getNo(); // 获取序号
            if (StringUtils.isNoneBlank(rule.getNormParams())) {// 超高质量normparams是空
                String[] resolutions = rule.getNormParams().replaceAll(" ", "").split("或");
                for (String resolution : resolutions) {
                    String[] split = resolution.split(RESOLUTION_CONNECTOR);
                    int width = Integer.parseInt(split[0]);
                    int height = Integer.parseInt(split[1]);
                    sortedNorms.add(new ImageMatchResult(normName, width, height, no, originalWidth, originalHeight));
                }
            }
        }
        // 按照分辨率大小排序
        sortedNorms.sort((a, b) -> {
            int areaA = a.getWidth() * a.getHeight();
            int areaB = b.getWidth() * b.getHeight();
            return Integer.compare(areaA, areaB);
        });
        // 如果原始分辨率小于最小标准，则直接用原始分辨率
        if (!sortedNorms.isEmpty()) {
            ImageMatchResult smallestNorm = sortedNorms.get(0);
            if (originalWidth < smallestNorm.getWidth() && originalHeight < smallestNorm.getHeight()) {
                return new ImageMatchResult("", originalWidth, originalHeight, 0, originalWidth, originalHeight);
            }
        }
        // 找到适合的规格：原始分辨率大于等于当前规格，但小于下一个规格
        ImageMatchResult result = null;
        ImageMatchResult tmp = null;
        //原始比例
        double originalRatio = (double) originalWidth / originalHeight;
        for (int i = 0; i < sortedNorms.size() - 1; i++) {
            ImageMatchResult currentNorm = sortedNorms.get(i);
            tmp = new ImageMatchResult(currentNorm.getNormName(), currentNorm.getWidth(), currentNorm.getHeight(), currentNorm.getNo(), originalWidth, originalHeight);
            int currentWidth = currentNorm.getWidth();
            int currentHeight = currentNorm.getHeight();
            if ((originalWidth >= currentWidth) ||
                    (originalHeight >= currentHeight)) {
                tmp = currentNorm;
                if (originalWidth >= currentWidth) {//按照宽度等比压缩，高度等于原始高度乘以当前宽度除以原始宽度，以保证比例不变
                    int height = (int) (originalHeight * originalRatio);
                    tmp.setHeight(height);
                }
                if (originalHeight >= currentHeight) {//按照高度等比压缩
                    int width = (int) (originalWidth * originalRatio);
                    tmp.setWidth(width);
                }
                //两个分辨率都大于
                if (originalWidth >= currentWidth && originalHeight >= currentHeight) {
                    tmp.setBoth(true);
                }
                result = tmp;
            }
        }
        if (result != null) {// 判断真实分辨率是否超高质量，也就是大于3840x2160
            if (originalWidth > 3840 || originalHeight > 2160) {
                return new ImageMatchResult(HIGH_QUALITY, originalWidth, originalHeight, 4, originalWidth,
                        originalHeight);
            }
            return result;
        }
        // 如果到达这里，说明原始分辨率大于所有规格，或者没有找到合适的规格
        return new ImageMatchResult(HIGH_QUALITY, originalWidth, originalHeight, 4, originalWidth, originalHeight);
    }

    /**
     * 处理三级类目和名称（一级和二级暂时不单独记录）
     *
     * @return
     */
    public void handle3Catalog(UjcmsMaterial ujcmsMaterial) {
        if (StringUtils.isNotBlank(ujcmsMaterial.getCatalogNo())) {
            String[] split = ujcmsMaterial.getCatalogNo().split(",");
            if (split.length == 3) {
                ujcmsMaterial.setOneCatalogNo(split[0]);
                Catalog catalog = catalogMapper.selectById(Integer.parseInt(split[0]));
                if (catalog != null) {
                    ujcmsMaterial.setOneCatalogName(catalog.getCatalogName());
                }
            }
        }
    }

    public ResponseEntity<UjcmsMaterial> create(UjcmsMaterial ujcmsMaterial) throws Exception {
        ujcmsMaterial.setId(IdUtil.getSnowflakeNextId());
        // 如果上架，设置发布时间
        if (ujcmsMaterial.getOnsale() == 1) {
            ujcmsMaterial.setPublisher(SecurityUtils.getUser().getUsername());
            ujcmsMaterial.setPublishTime(new Date());
        }
        String srcFilePath = uploadPath + ujcmsMaterial.getFilePath();
        // 原始文件大小
        ujcmsMaterial.setSize(FileUtil.size(new File(srcFilePath)));
        ujcmsMaterial.setMd5(FileUtils.getMd5(srcFilePath));
        ujcmsMaterialMapper.insert(ujcmsMaterial);
        //图片才需要压缩
        if (MaterialEnum.IMG.getCode().equals(ujcmsMaterial.getType())) {
            // 匹配文件规格
            ImageMatchResult stringIntegerIntegerTriple = matchImage(ujcmsMaterial.getFilePath(),
                    defaultPricingStrategyRules);
            ujcmsMaterial.setNorm(stringIntegerIntegerTriple.getNormName());
            // 压缩
            compressImage(ujcmsMaterial.getFilePath(), ujcmsMaterial, stringIntegerIntegerTriple);
            //logo
            if (StringUtils.isBlank(ujcmsMaterial.getLogo())) {
                ujcmsMaterial.setLogo(ujcmsMaterial.getUrl());
            }
            //预览图按照最低的普通质量进一步压缩并添加文字水印，并且将logo设置为预览图
            String previewPath = ujcmsMaterial.getFilePath().replace(OrderService.PATH_REPLACE, PREVIEW_FOLDER_PREFIX);
            String previewFilePath = uploadPath + previewPath;
            Integer targetWidth = stringIntegerIntegerTriple.getSrcWidth();
            Integer targetHeight = stringIntegerIntegerTriple.getSrcHeight();
            // 计算宽高比
            double aspectRatio = (double) targetWidth / targetHeight;
            // 如果宽度超过1920，以宽度为基准压缩
            if (targetWidth > 1920) {
                targetWidth = 1920;
                targetHeight = (int) (1920 / aspectRatio);
            }
            // 如果高度仍然超过1080，以高度为基准再次压缩
            if (targetHeight > 1080) {
                targetHeight = 1080;
                targetWidth = (int) (1080 * aspectRatio);
            }
            ImageCompressUtil.resizeImage(srcFilePath, previewFilePath, targetWidth, targetHeight, false);
            ImageWatermarkUtil.addWatermarkAndSave(previewFilePath,
                    previewFilePath, "数字武当", 0.5f, 24, Color.WHITE,
                    "tiled");
            ujcmsMaterial.setPreviewFilePath(previewPath);
            ujcmsMaterial.setPreviewUrl(url + previewPath);
            //logo
            if (StringUtils.isBlank(ujcmsMaterial.getLogo())) {
                ujcmsMaterial.setLogo(ujcmsMaterial.getPreviewUrl());
            }
        }
        //音视频需要截取30秒用于预览
        if (MaterialEnum.VIDEO.getCode().equals(ujcmsMaterial.getType()) ||
                MaterialEnum.AUDIO.getCode().equals(ujcmsMaterial.getType())) {
            String videoPreviewUrl = url + ujcmsMaterial.getFilePath().replace(".", PREVIEW_SUFFIX);
            String videoPreviewPath = uploadPath + ujcmsMaterial.getFilePath().replace(".", PREVIEW_SUFFIX);
            ffmpegUtil.trimVideo(srcFilePath, videoPreviewPath);
            ujcmsMaterial.setPreviewUrl(videoPreviewUrl);
            //视频，需要截取第一帧作为封面
            if (MaterialEnum.VIDEO.getCode().equals(ujcmsMaterial.getType()) && StringUtils.isBlank(ujcmsMaterial.getLogo())) {
                String imagePreviewUrl = url + ujcmsMaterial.getFilePath().replace(".", FIRST_FRAME_SUFFIX).replace(ujcmsMaterial.getFormat(), DEFAULT_VIDEO_FRAME_FORMAT);
                String imagePreviewPath = uploadPath + ujcmsMaterial.getFilePath().replace(".", FIRST_FRAME_SUFFIX).replace(ujcmsMaterial.getFormat(), DEFAULT_VIDEO_FRAME_FORMAT);
                ffmpegUtil.extractFirstFrameFromVideo(srcFilePath, imagePreviewPath);
                ujcmsMaterial.setLogo(imagePreviewUrl);
            }
        }
        handleCategoryAndCatalog(ListBuilder.<UjcmsMaterial>ofList().add(ujcmsMaterial).build());
        // 处理三级类目和名称
        handle3Catalog(ujcmsMaterial);
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
        // 给原始文件加水印
        ImageWatermarkUtil.addWatermark(srcFilePath,
                srcFilePath, ujcmsMaterial.getId().toString(), 0.0f, 24, Color.WHITE,
                "bottom_right");
        // 写入授权文件数据
        saveAuthfiles(ujcmsMaterial.getAuthFiles(), ujcmsMaterial.getId());
        commonEs8ClientUtil.writeData(ujcmsMaterial, ujcmsMaterial.getId().toString(), indexName);
        return ResponseEntity.ok(ujcmsMaterial);
    }

    /**
     * 写入授权文件数据
     *
     * @param authFiles
     */
    public void saveAuthfiles(List<UjcmsMateialAuthFile> authFiles, Long materialId) {
        if (CollectionUtils.isNotEmpty(authFiles)) {
            // 先删除旧数据
            ujcmsMateialAuthFileMapper.deleteByMaterialId(materialId);
            for (UjcmsMateialAuthFile authFile : authFiles) {
                authFile.setMaterialId(materialId);
                ujcmsMateialAuthFileMapper.insert(authFile);
            }
        }
    }

    public ResponseEntity<UjcmsMaterial> update(UjcmsMaterial ujcmsMaterial) throws Exception {
        if (ujcmsMaterial.getOnsale() == 1) {
            ujcmsMaterial.setPublisher(SecurityUtils.getUser().getUsername());
            ujcmsMaterial.setPublishTime(new Date());
        }
        UjcmsMaterial oldUjcmsMaterial = ujcmsMaterialMapper.selectById(ujcmsMaterial.getId());
        if (MaterialEnum.IMG.getCode().equals(ujcmsMaterial.getType())) {//图片才压缩
            // 如果更换了文件，需要重新匹配文件规格
            if (!oldUjcmsMaterial.getFilePath().equals(ujcmsMaterial.getFilePath())) {
                String srcFilePath = uploadPath + ujcmsMaterial.getFilePath();
                ujcmsMaterial.setSize(FileUtil.size(new File(srcFilePath)));
                ujcmsMaterial.setMd5(FileUtils.getMd5(srcFilePath));
                // 先删除原有的素材可选规格
                ujcmsMaterialPriceMapper.deleteByMaterialId(ujcmsMaterial.getId());
                ImageMatchResult stringIntegerIntegerTriple = matchImage(ujcmsMaterial.getFilePath(),
                        defaultPricingStrategyRules);
                ujcmsMaterial.setNorm(stringIntegerIntegerTriple.getNormName());
                compressImage(ujcmsMaterial.getFilePath(), ujcmsMaterial, stringIntegerIntegerTriple);
                // 加水印
                ImageWatermarkUtil.addWatermark(srcFilePath,
                        srcFilePath, ujcmsMaterial.getId().toString(), 0.0f, 24, Color.WHITE,
                        "bottom_right");
                //logo
                if (StringUtils.isBlank(ujcmsMaterial.getLogo())) {
                    ujcmsMaterial.setLogo(ujcmsMaterial.getUrl());
                }
                ujcmsMaterial.setPreviewUrl(ujcmsMaterial.getUrl());
            }
        }
        //音视频需要截取30秒用于预览
        String srcFilePath = uploadPath + ujcmsMaterial.getFilePath();
        if (MaterialEnum.VIDEO.getCode().equals(ujcmsMaterial.getType()) ||
                MaterialEnum.AUDIO.getCode().equals(ujcmsMaterial.getType())) {
            String videoPreviewUrl = url + ujcmsMaterial.getFilePath().replace(".", PREVIEW_SUFFIX);
            String videoPreviewPath = uploadPath + ujcmsMaterial.getFilePath().replace(".", PREVIEW_SUFFIX);
            ffmpegUtil.trimVideo(srcFilePath, videoPreviewPath);
            ujcmsMaterial.setPreviewUrl(videoPreviewUrl);
            //视频，需要截取第一帧作为封面
            if (MaterialEnum.VIDEO.getCode().equals(ujcmsMaterial.getType()) && StringUtils.isBlank(ujcmsMaterial.getLogo())) {
                String imagePreviewPath = uploadPath + ujcmsMaterial.getFilePath().replace(".", FIRST_FRAME_SUFFIX).replace(ujcmsMaterial.getFormat(), DEFAULT_VIDEO_FRAME_FORMAT);
                String imagePreviewUrl = url + ujcmsMaterial.getFilePath().replace(".", FIRST_FRAME_SUFFIX).replace(ujcmsMaterial.getFormat(), DEFAULT_VIDEO_FRAME_FORMAT);
                ffmpegUtil.extractFirstFrameFromVideo(srcFilePath, imagePreviewPath);
                ujcmsMaterial.setLogo(imagePreviewUrl);
            }
        }
        List<UjcmsMateialAuthFile> authFiles = ujcmsMaterial.getAuthFiles();
        handleCategoryAndCatalog(ListBuilder.<UjcmsMaterial>ofList().add(ujcmsMaterial).build());
        // 处理三级类目和名称
        handle3Catalog(ujcmsMaterial);
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
        // 记录授权文件数据
        saveAuthfiles(authFiles, ujcmsMaterial.getId());
        commonEs8ClientUtil.writeData(ujcmsMaterial, ujcmsMaterial.getId().toString(), indexName);
        return ResponseEntity.ok(ujcmsMaterial);
    }

    public ResponseEntity<Page<UjcmsMaterial>> page(UjcmsMaterialArgs args) {
        // 返回包含分页信息和内容的 ResponseEntity
        return ResponseEntity.ok(MyBatis.springPage(PageMethod.startPage(args.getPageNum(), args.getPageSize())
                .doSelectPage(() -> list(args))));
    }

    /**
     * 查询定价方案名称和类目名称
     *
     * @param list
     */
    private void handleCategoryAndCatalog(List<UjcmsMaterial> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询定价方案名称
            List<Integer> pricingStrategyIds = list.stream().map(UjcmsMaterial::getPricingStrategyId).distinct()
                    .toList();
            if (CollectionUtils.isNotEmpty(pricingStrategyIds)) {
                List<UjcmsPricingStrategy> ujcmsPricingStrategies = ujcmsPricingStrategyMapper
                        .selectBatchIds(pricingStrategyIds);
                Map<Integer, UjcmsPricingStrategy> strategyMap = ujcmsPricingStrategies.parallelStream().collect(
                        Collectors.toMap(UjcmsPricingStrategy::getId, ujcmsPricingStrategy -> ujcmsPricingStrategy));
                for (UjcmsMaterial ujcmsMaterial : list) {
                    UjcmsPricingStrategy ujcmsPricingStrategy = strategyMap.get(ujcmsMaterial.getPricingStrategyId());
                    if (ujcmsPricingStrategy != null) {
                        ujcmsMaterial.setPricingStrategyName(ujcmsPricingStrategy.getName());
                    }
                }
                // 定价方案规则
                List<UjcmsPricingStrategyRule> ujcmsPricingStrategyRules = ujcmsPricingStrategyRuleMapper
                        .selectPricingStrategyRules(pricingStrategyIds);
                Map<Integer, List<UjcmsPricingStrategyRule>> strategyRuleMap = ujcmsPricingStrategyRules.parallelStream()
                        .collect(
                                Collectors.groupingBy(UjcmsPricingStrategyRule::getUjcmsPricingStrategyId));
                for (UjcmsMaterial ujcmsMaterial : list) {
                    ujcmsMaterial.setPricingStrategyRules(strategyRuleMap.get(ujcmsMaterial.getPricingStrategyId()));
                }
            }
            // 可选定价方案
            List<UjcmsMaterialPrice> ujcmsMaterialPrices = ujcmsMaterialPriceMapper.selectPrices(
                    list.parallelStream().map(UjcmsMaterial::getId).distinct().collect(Collectors.toList()));
            Map<Long, List<UjcmsMaterialPrice>> pricingStrategyMap = ujcmsMaterialPrices.parallelStream()
                    .collect(Collectors.groupingBy(UjcmsMaterialPrice::getMaterialId));
            for (UjcmsMaterial ujcmsMaterial : list) {
                ujcmsMaterial.setUjcmsMaterialPrices((pricingStrategyMap.get(ujcmsMaterial.getId())));
            }
            // 查询类目名称，类目编号是用逗号隔开的
            List<String> catalogIds = list.stream().map(UjcmsMaterial::getCatalogNo).collect(Collectors.toList());
            // catalogIds每一个都是逗号拼接的，需要按照逗号切割开转换为int汇总到一个list中
            if (CollectionUtils.isNotEmpty(catalogIds)) {
                List<Integer> catalogIdList = new ArrayList<>();
                for (String catalogId : catalogIds) {
                    String[] split = catalogId.split(",");
                    for (String s : split) {
                        catalogIdList.add(Integer.parseInt(s));
                    }
                }
                catalogIdList = catalogIdList.parallelStream().distinct().toList();
                List<Catalog> catalogs = catalogMapper.selectBatchIds(catalogIdList);
                Map<Integer, Catalog> catalogMap = catalogs.parallelStream()
                        .collect(Collectors.toMap(Catalog::getId, catalog -> catalog));
                for (UjcmsMaterial ujcmsMaterial : list) {
                    String catalogNo = ujcmsMaterial.getCatalogNo();
                    String[] split = catalogNo.split(",");
                    StringBuilder stringBuilder = new StringBuilder();
                    for (String s : split) {
                        Catalog catalog = catalogMap.get(Integer.parseInt(s));
                        if (catalog != null) {
                            stringBuilder.append(catalog.getCatalogName()).append(",");
                        }
                    }
                    if (stringBuilder.length() > 0) {
                        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                        ujcmsMaterial.setCatalogName(stringBuilder.toString());
                    }
                }
            }
            // 查询授权文件
            List<Long> materialIds = list.stream().filter(ujcmsMaterial -> ujcmsMaterial.getId() != null)
                    .map(UjcmsMaterial::getId).distinct().toList();
            if (CollectionUtils.isNotEmpty(materialIds)) {
                List<UjcmsMateialAuthFile> ujcmsAuthfiles = ujcmsMateialAuthFileMapper
                        .selectByBatchMaterialId(materialIds);
                Map<Long, List<UjcmsMateialAuthFile>> authfilesMap = ujcmsAuthfiles.parallelStream()
                        .collect(Collectors.groupingBy(UjcmsMateialAuthFile::getMaterialId));
                for (UjcmsMaterial ujcmsMaterial : list) {
                    List<UjcmsMateialAuthFile> files = authfilesMap.get(ujcmsMaterial.getId());
                    if (files != null) {
                        ujcmsMaterial.setAuthFiles(files);
                    }
                }
            }
        }
    }

    private List<UjcmsMaterial> list(UjcmsMaterialArgs args) {
        List<UjcmsMaterial> list = ujcmsMaterialMapper.list(args);
        handleCategoryAndCatalog(list);
        return list;
    }

    public ResponseEntity<String> createBatch(List<UjcmsMaterial> ujcmsMaterialList) throws Exception {
        //批量导入的任务id
        String taskId = TASK_ID_PREFIX + IdUtil.getSnowflakeNextIdStr();
        List<UjcmsMaterial> successList = new ArrayList<>();
        List<UjcmsMaterial> failList = new ArrayList<>();
        JSONObject progress = new JSONObject();
        progress.put("success", successList);
        progress.put("fail", failList);
        //进度
        progress.put("progress", 0);
        stringRedisTemplate.opsForValue().set(BATCH_IMPORT_MATERIAL_TASK_CACHE_KEY_PREFIX + taskId,
                JSON.toJSONString(progress), 1, TimeUnit.DAYS);
        for (UjcmsMaterial ujcmsMaterial : ujcmsMaterialList) {
            // 如果上架，设置发布时间
            if (ujcmsMaterial.getOnsale() == 1) {
                ujcmsMaterial.setPublisher(SecurityUtils.getUser().getUsername());
                ujcmsMaterial.setPublishTime(new Date());
            }
        }
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            for (UjcmsMaterial ujcmsMaterial : ujcmsMaterialList) {
                try {
                    importMaterial(ujcmsMaterial);
                    successList.add(ujcmsMaterial);
                } catch (Exception e) {
                    failList.add(ujcmsMaterial);
                    LOGGER.error("批量导入失败", e);
                    LOGGER.info("import_fail:{}", JSON.toJSONString(ujcmsMaterial));
                }
                progress.put("success", successList);
                progress.put("fail", failList);
                //进度
                progress.put("progress", 1.0 * (successList.size() + failList.size()) / ujcmsMaterialList.size() * 100);
                stringRedisTemplate.opsForValue().set(BATCH_IMPORT_MATERIAL_TASK_CACHE_KEY_PREFIX + taskId,
                        JSON.toJSONString(progress), 1, TimeUnit.DAYS);
            }
        });
        return ResponseEntity.ok(taskId);
    }

    private void importMaterial(UjcmsMaterial ujcmsMaterial) {
        ujcmsMaterial.setId(IdUtil.getSnowflakeNextId());
        //如果是从资源管理平台导入的，需要先下载文件到本地
        if (StringUtils.isBlank(ujcmsMaterial.getFilePath())) {
            Site site = siteMapper.select(1l);
            String filePath = site.getBasePath(SLASH + "file") + Uploads.getRandomFilename(ujcmsMaterial.getFormat());
            FileUtils.download(ujcmsMaterial.getUrl(), uploadPath + filePath);
            if (!FileUtil.exist(uploadPath + filePath)) {
                throw new RuntimeException("文件下载失败");
            }
            ujcmsMaterial.setFilePath(filePath);
            ujcmsMaterial.setUrl(url + filePath);
        }
        if (MaterialEnum.IMG.getCode().equals(ujcmsMaterial.getType())) {//图片才压缩
            ImageMatchResult stringIntegerIntegerTriple = matchImage(ujcmsMaterial.getFilePath(),
                    defaultPricingStrategyRules);
            compressImage(ujcmsMaterial.getFilePath(), ujcmsMaterial, stringIntegerIntegerTriple);
            //logo
            if (StringUtils.isBlank(ujcmsMaterial.getLogo())) {
                ujcmsMaterial.setLogo(ujcmsMaterial.getUrl());
            }
            ujcmsMaterial.setPreviewUrl(ujcmsMaterial.getUrl());
        }
        //音视频需要截取30秒用于预览
        String srcFilePath = uploadPath + ujcmsMaterial.getFilePath();
        ujcmsMaterial.setSize(FileUtil.size(new File(srcFilePath)));
        ujcmsMaterial.setMd5(FileUtils.getMd5(srcFilePath));
        //音视频需要截取30秒用于预览
        if (MaterialEnum.VIDEO.getCode().equals(ujcmsMaterial.getType()) ||
                MaterialEnum.AUDIO.getCode().equals(ujcmsMaterial.getType())) {
            String videoPreviewUrl = url + ujcmsMaterial.getFilePath().replace(".", PREVIEW_SUFFIX);
            String videoPreviewPath = uploadPath + ujcmsMaterial.getFilePath().replace(".", PREVIEW_SUFFIX);
            ffmpegUtil.trimVideo(srcFilePath, videoPreviewPath);
            ujcmsMaterial.setPreviewUrl(videoPreviewUrl);
            //视频，需要截取第一帧作为封面
            if (MaterialEnum.VIDEO.getCode().equals(ujcmsMaterial.getType()) && StringUtils.isBlank(ujcmsMaterial.getLogo())) {
                String imagePreviewPath = uploadPath + ujcmsMaterial.getFilePath().replace(".", FIRST_FRAME_SUFFIX).replace(ujcmsMaterial.getFormat(), DEFAULT_VIDEO_FRAME_FORMAT);
                String imagePreviewUrl = url + ujcmsMaterial.getFilePath().replace(".", FIRST_FRAME_SUFFIX).replace(ujcmsMaterial.getFormat(), DEFAULT_VIDEO_FRAME_FORMAT);
                ffmpegUtil.extractFirstFrameFromVideo(srcFilePath, imagePreviewPath);
                ujcmsMaterial.setLogo(imagePreviewUrl);
            }
        }
        // 处理三级类目和名称
        handle3Catalog(ujcmsMaterial);
        ujcmsMaterialMapper.insert(ujcmsMaterial);
        // 写入授权文件数据
        saveAuthfiles(ujcmsMaterial.getAuthFiles(), ujcmsMaterial.getId());
        handleCategoryAndCatalog(ListBuilder.<UjcmsMaterial>ofList().add(ujcmsMaterial).build());
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
        commonEs8ClientUtil.writeData(ujcmsMaterial, ujcmsMaterial.getId().toString(), indexName);
    }

    public ResponseEntity<String> delete(List<Long> ids) {
        // 已经上架和存在待支付订单的素材禁止删除
        for (Long id : ids) {
            UjcmsMaterial ujcmsMaterial = ujcmsMaterialMapper.selectById(id);
            if (ujcmsMaterial.getOnsale() == 1) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ujcmsMaterial.getName() + "已经上架的素材禁止删除");
            }
            if (orderMapper.selectCountByMaterialId(id) != null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ujcmsMaterial.getName() + "存在待支付的订单，禁止删除");
            }
        }
        for (Long id : ids) {
            ujcmsMaterialMapper.deleteById(id);
            commonEs8ClientUtil.deleteData(id + "", indexName);
            //删除收藏数据
            userMaterialCollectMapper.deleteByMaterialId(id);
        }
        return ResponseEntity.ok(OperationConstants.SUCCESS);
    }

    public ResponseEntity<UjcmsMaterial> detail(long id) {
        UjcmsMaterial ujcmsMaterial = ujcmsMaterialMapper.selectById(id);
        ujcmsMaterial.setAccessCount(ujcmsMaterial.getAccessCount() == null ? 0 : ujcmsMaterial.getAccessCount() + 1);
        commonEs8ClientUtil.writeData(ujcmsMaterial, ujcmsMaterial.getId().toString(), indexName);
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
        handleCategoryAndCatalog(Collections.singletonList(ujcmsMaterial));
        return ResponseEntity.ok(ujcmsMaterial);
    }

    public ResponseEntity<UjcmsMaterial> detail(long id, Long userId) {
        UjcmsMaterial ujcmsMaterial = ujcmsMaterialMapper.selectById(id);
        ujcmsMaterial.setAccessCount(ujcmsMaterial.getAccessCount() == null ? 0 : ujcmsMaterial.getAccessCount() + 1);
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
        commonEs8ClientUtil.writeData(ujcmsMaterial, ujcmsMaterial.getId().toString(), indexName);
        handleCategoryAndCatalog(Collections.singletonList(ujcmsMaterial));
        List<Long> materialIds = userMaterialCollectMapper.getByUserId(userId);
        if (CollectionUtils.isNotEmpty(materialIds)) {
            if (materialIds.contains(ujcmsMaterial.getId())) {
                ujcmsMaterial.setCollected(1);
            }
        }
        return ResponseEntity.ok(ujcmsMaterial);
    }

    public ResponseEntity<String> onsale(List<UjcmsMaterialArgs> args) {
        for (UjcmsMaterialArgs arg : args) {
            UjcmsMaterial ujcmsMaterial = new UjcmsMaterial();
            ujcmsMaterial.setId(arg.getId());
            ujcmsMaterial.setOnsale(arg.getOnsale());
            if (1 == arg.getOnsale()) {
                ujcmsMaterial.setPublishTime(new Date());
                ujcmsMaterial.setPublisher(SecurityUtils.getUser().getUsername());
            }
            ujcmsMaterialMapper.updateById(ujcmsMaterial);
            commonEs8ClientUtil.updateData(indexName,
                    ListBuilder.<String>ofList().add(ujcmsMaterial.getId().toString()).build(),
                    ListBuilder.<UjcmsMaterial>ofList().add(ujcmsMaterial).build());
        }
        return ResponseEntity.ok(OperationConstants.SUCCESS);
    }

    public ResponseEntity<String> homepage(long id, int homePage) {
        UjcmsMaterial ujcmsMaterial = new UjcmsMaterial();
        ujcmsMaterial.setId(id);
        ujcmsMaterial.setHomePage(homePage);
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
        commonEs8ClientUtil.updateData(indexName,
                ListBuilder.<String>ofList().add(ujcmsMaterial.getId().toString()).build(),
                ListBuilder.<UjcmsMaterial>ofList().add(ujcmsMaterial).build());
        return ResponseEntity.ok(OperationConstants.SUCCESS);
    }

    /**
     * 从UjcmsMaterialArgs构建es请求参数
     *
     * @param args
     * @return
     */
    private BoolQuery.Builder buildSearchRequest(UjcmsMaterialArgs args) {
        BoolQuery.Builder mustQueryBuilder = new BoolQuery.Builder();
        // 精确匹配 catalogNo 中包含 "1" 的记录
        // 使用正则表达式匹配：以1开头、以1结尾或者1前后是逗号
        String catalogNoValue = args.getCatalogNo();
        if (StringUtils.isNotBlank(catalogNoValue)) {
            // "should": [
            // { "wildcard": { "catalogNo": "2,*" } },
            // { "wildcard": { "catalogNo": "*,2,*" } },
            // { "wildcard": { "catalogNo": "*,2" } },
            // { "term": { "catalogNo": "2" } }
            // ]
            BoolQuery.Builder shouldQueryBuilder = new BoolQuery.Builder();
            shouldQueryBuilder
                    .should(WildcardQuery.of(m -> m.field("catalogNo").value(catalogNoValue + ",*"))._toQuery());
            shouldQueryBuilder
                    .should(WildcardQuery.of(m -> m.field("catalogNo").value("*," + catalogNoValue + ",*"))._toQuery());
            shouldQueryBuilder
                    .should(WildcardQuery.of(m -> m.field("catalogNo").value("*," + catalogNoValue))._toQuery());
            shouldQueryBuilder.should(Query.of(q -> q.term(t -> t.field("catalogNo").value(catalogNoValue))));
            mustQueryBuilder.must(shouldQueryBuilder.build()._toQuery());
        }
        String keyword = args.getKeyword();
        if (StringUtils.isNotBlank(keyword)) {
            // 记录热门搜索关键词
            if (Integer.valueOf(1).equals(args.getRecordHotWord())) {
                addHotSearch(keyword);
            }
            BoolQuery.Builder shouldQueryBuilder = new BoolQuery.Builder();
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("name").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("catalogName").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("relatedSource").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("tag").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("overview").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("author").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("publisher").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("pricingStrategyName").query(keyword))));
            shouldQueryBuilder.should(Query.of(q -> q.match(t -> t.field("norm").query(keyword))));
            List<Query> shouldQueries = List.of(shouldQueryBuilder.build()._toQuery());
            mustQueryBuilder.must(shouldQueries);
        }
        return mustQueryBuilder;
    }

    private void addHotSearch(String keyword) {
        CmsQueues.KEYWORD_QUEUE.add(keyword);
    }

    public ResponseEntity<PageImpl<UjcmsMaterial>> search(UjcmsMaterialArgs args) {
        BoolQuery.Builder boolQueryBuilder = buildSearchRequest(args);
        // 关联资源
        if (StringUtils.isNotBlank(args.getRelatedSource())) {
            boolQueryBuilder
                    .must(Query.of(q -> q.term(t -> t.field("relatedSource.keyword").value(args.getRelatedSource()))));
        }
        // 格式
        if (StringUtils.isNotBlank(args.getFormat())) {
            boolQueryBuilder.must(Query.of(q -> q.term(t -> t.field("format").value(args.getFormat()))));
        }
        // 规格
        if (StringUtils.isNotBlank(args.getNorm())) {
            boolQueryBuilder.must(Query.of(q -> q.term(t -> t.field("norm.keyword").value(args.getNorm()))));
        }
        // 是否首页展示
        if (args.getHomePage() != null) {
            boolQueryBuilder.must(Query.of(q -> q.term(t -> t.field("homePage").value(args.getHomePage()))));
        }
        // 只展示已经上架的
        boolQueryBuilder.must(Query.of(q -> q.term(t -> t.field("onsale").value(1))));
        Query query = Query.of(q -> q.bool(boolQueryBuilder.build()));
        SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder();
        searchRequestBuilder.trackTotalHits(t -> t.enabled(true));
        searchRequestBuilder.index(indexName);
        searchRequestBuilder.size(args.getPageSize());
        searchRequestBuilder.from((args.getPageNum() - 1) * args.getPageSize());
        searchRequestBuilder.query(query);
        // 排序
        if (StringUtils.isNotBlank(args.getSortField())) {
            searchRequestBuilder.sort(SortOptionsBuilders
                    .field(s -> s.field(args.getSortField()).order(SortOrder.valueOf(args.getSortOrder()))));
        }
        SearchRequest searchRequest = searchRequestBuilder.build();
        LOGGER.info("searchRequest:{}", searchRequest.toString());
        SearchResponse<UjcmsMaterial> response = commonEs8ClientUtil.searchData(searchRequest, indexName,
                UjcmsMaterial.class);
        List<UjcmsMaterial> data = new ArrayList<>();
        for (Hit<UjcmsMaterial> hit : response.hits().hits()) {
            data.add(hit.source());
        }
        // 如果是登录状态，判断当前用户是否收藏
        if (args.getUserId() != null) {
            List<Long> materialIds = userMaterialCollectMapper.getByUserId(args.getUserId());
            if (CollectionUtils.isNotEmpty(materialIds)) {
                for (UjcmsMaterial ujcmsMaterial : data) {
                    if (materialIds.contains(ujcmsMaterial.getId())) {
                        ujcmsMaterial.setCollected(1);
                    }
                }
            }
        }
        long total = response.hits().total().value();
        PageImpl<UjcmsMaterial> hits = new PageImpl<>(data, PageRequest.of(args.getPageNum() - 1,
                args.getPageSize()), total);
        return ResponseEntity.ok(hits);
    }

    public ResponseEntity<List<StasticsItem>> statistics(UjcmsMaterialArgs args) {
        List<StasticsItem> stasticsItems = new LinkedList<>();
        // 全部
        StasticsItem stasticsItem = new StasticsItem();
        stasticsItem.setName("全部");
        stasticsItems.add(stasticsItem);
        SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder();
        searchRequestBuilder.size(0);
        String key = "";
        String field = "";
        // builder只能调用一次build方法，所以需要重新构建
        BoolQuery.Builder builder = buildSearchRequest(args);
        builder.must(Query.of(q -> q.term(t -> t.field("onsale").value(1))));
        searchRequestBuilder = new SearchRequest.Builder();
        searchRequestBuilder.size(0);
        BoolQuery.Builder finalBuilder = builder;
        BoolQuery.Builder finalBuilder2 = finalBuilder;
        switch (args.getType()) {
            case 1:// 按照关联资源统计relatedSource
                key = "relatedSource";
                field = "relatedSource.keyword";
                finalBuilder2
                        .must(Query.of(q -> q.exists(e -> e.field("relatedSource.keyword"))))
                        .mustNot(Query.of(q -> q.term(t -> t.field("relatedSource.keyword").value(""))));
                searchRequestBuilder.query(Query.of(q -> q.bool(finalBuilder2.build())));
                searchRequestBuilder.aggregations(key,
                        a -> a.terms(t -> t.field("relatedSource.keyword").size(1000)));
                SearchResponse<UjcmsMaterial> response = commonEs8ClientUtil.searchData(searchRequestBuilder.build(),
                        indexName, UjcmsMaterial.class);
                assembleStatisticsResult(stasticsItems, response, key);
                break;
            case 2:// 格式
                key = "format";
                field = "format";
                finalBuilder2
                        .must(Query.of(q -> q.exists(e -> e.field("format"))))
                        .mustNot(Query.of(q -> q.term(t -> t.field("format").value(""))));
                searchRequestBuilder.query(Query.of(q -> q.bool(finalBuilder2.build())));
                searchRequestBuilder.aggregations(key, a -> a.terms(t -> t.field("format").size(1000)));
                SearchResponse<UjcmsMaterial> response1 = commonEs8ClientUtil.searchData(searchRequestBuilder.build(),
                        indexName, UjcmsMaterial.class);
                assembleStatisticsResult(stasticsItems, response1, key);
                break;
            case 3:// 质量
                key = "norm";
                field = "norm.keyword";
                finalBuilder2
                        .must(Query.of(q -> q.exists(e -> e.field("norm.keyword"))))
                        .mustNot(Query.of(q -> q.term(t -> t.field("norm.keyword").value(""))));
                searchRequestBuilder.query(Query.of(q -> q.bool(finalBuilder2.build())));
                searchRequestBuilder.aggregations(key, a -> a.terms(t -> t.field("norm.keyword").size(1000)));
                SearchResponse<UjcmsMaterial> response2 = commonEs8ClientUtil.searchData(searchRequestBuilder.build(),
                        indexName, UjcmsMaterial.class);
                assembleStatisticsResult(stasticsItems, response2, key);
                break;
            case 4:// 分类
                key = "oneCatalogName";
                field = "oneCatalogName";
                finalBuilder2
                        .must(Query.of(q -> q.exists(e -> e.field("oneCatalogName"))))
                        .mustNot(Query.of(q -> q.term(t -> t.field("oneCatalogName").value(""))));
                searchRequestBuilder.query(Query.of(q -> q.bool(finalBuilder2.build())));
                searchRequestBuilder.aggregations(key,
                        a -> a.terms(t -> t.field("oneCatalogName").size(1000)));
                SearchResponse<UjcmsMaterial> response3 = commonEs8ClientUtil.searchData(searchRequestBuilder.build(),
                        indexName, UjcmsMaterial.class);
                assembleStatisticsResult(stasticsItems, response3, key);
                break;

        }
        // 从es查询全部
        searchRequestBuilder = new SearchRequest.Builder();
        searchRequestBuilder.size(0);
        //field必须存在
        finalBuilder = buildSearchRequest(args);
        String finalField = field;
        finalBuilder.must(Query.of(q -> q.exists(t -> t.field(finalField))))
                .mustNot(Query.of(q -> q.term(t -> t.field(finalField).value(""))));
        finalBuilder.must(Query.of(q -> q.term(t -> t.field("onsale").value(1))));
        BoolQuery.Builder finalBuilder1 = finalBuilder;
        searchRequestBuilder.query(Query.of(q -> q.bool(finalBuilder1.build())));
        SearchResponse<UjcmsMaterial> ujcmsMaterialSearchResponse = commonEs8ClientUtil
                .searchData(searchRequestBuilder.build(), indexName, UjcmsMaterial.class);
        stasticsItem.setValue(new BigDecimal(ujcmsMaterialSearchResponse.hits().total().value()));
        return ResponseEntity.ok(stasticsItems);
    }

    // 组装统计结果
    private void assembleStatisticsResult(List<StasticsItem> stasticsItems, SearchResponse<UjcmsMaterial> response,
                                          String key) {
        for (Map.Entry<String, Aggregate> stringAggregateEntry : response.aggregations().entrySet()) {
            if (key.equals(stringAggregateEntry.getKey())) {
                Aggregate aggregate = stringAggregateEntry.getValue();
                Buckets<StringTermsBucket> buckets = aggregate.sterms().buckets();
                if (buckets != null) {
                    for (StringTermsBucket stringTermsBucket : buckets.array()) {
                        StasticsItem countResult = new StasticsItem();
                        countResult.setName(stringTermsBucket.key().stringValue());
                        countResult.setValue(new BigDecimal(stringTermsBucket.docCount()));
                        stasticsItems.add(countResult);
                    }
                }
            }
        }
    }

    public ResponseEntity<List<String>> hotKeywordSearch() {
        return ResponseEntity.ok(hotKeywordSearchMapper.selectTop10());
    }

    public UjcmsMaterial getById(Long id) {
        return ujcmsMaterialMapper.selectById(id);
    }

    public void incrementDownloadCount(UjcmsMaterial ujcmsMaterial) {
        ujcmsMaterial
                .setDownloadCount(ujcmsMaterial.getDownloadCount() == null ? 0 : ujcmsMaterial.getDownloadCount() + 1);
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
    }

    public ResponseEntity<String> collect(Long materialId, Long userId) {
        // 已经收藏，直接返回
        UserMaterialCollect ujcmsMaterialCollect = userMaterialCollectMapper.get(materialId, userId);
        if (ujcmsMaterialCollect == null) {
            ujcmsMaterialCollect = new UserMaterialCollect();
            ujcmsMaterialCollect.setMaterialId(materialId);
            ujcmsMaterialCollect.setUserId(userId);
            userMaterialCollectMapper.insert(ujcmsMaterialCollect);
        }
        UjcmsMaterial ujcmsMaterial = ujcmsMaterialMapper.selectById(materialId);
        ujcmsMaterial
                .setCollectCount(ujcmsMaterial.getCollectCount() == null ? 0 : ujcmsMaterial.getCollectCount() + 1);
        ujcmsMaterialMapper.updateById(ujcmsMaterial);
        return ResponseEntity.ok(OperationConstants.SUCCESS);
    }

    public ResponseEntity<String> uncollect(Long materialId, Long userId) {
        UserMaterialCollect ujcmsMaterialCollect = userMaterialCollectMapper.get(materialId, userId);
        if (ujcmsMaterialCollect != null) {
            userMaterialCollectMapper.deleteById(ujcmsMaterialCollect.getId());
            UjcmsMaterial ujcmsMaterial = ujcmsMaterialMapper.selectById(materialId);
            ujcmsMaterial
                    .setCollectCount(ujcmsMaterial.getCollectCount() == null ? 0 : ujcmsMaterial.getCollectCount() - 1);
            ujcmsMaterialMapper.updateById(ujcmsMaterial);
        }
        return ResponseEntity.ok(OperationConstants.SUCCESS);
    }

    public void recordDownload(Long materialId, Long userId) {
        UserMaterialDownload userMaterialDownload = userMaterialDownloadMapper.get(materialId, userId);
        if (userMaterialDownload == null) {
            userMaterialDownload = new UserMaterialDownload();
            userMaterialDownload.setMaterialId(materialId);
            userMaterialDownload.setUserId(userId);
            userMaterialDownloadMapper.insert(userMaterialDownload);
        }
    }

    public ResponseEntity<List<StasticsItem>> homePagestatistics() {
        List<StasticsItem> stasticsItems = new LinkedList<>();
        // 文化素材类型（类）：类目表没有子级类目的类目数量
        StasticsItem catalogItem = new StasticsItem();
        catalogItem.setName("文化素材类型（类）");
        Integer countNoChildCatalog = catalogMapper.countNoChildCatalog();
        catalogItem.setValue(new BigDecimal(countNoChildCatalog == null ? 0 : countNoChildCatalog));
        stasticsItems.add(catalogItem);
        // 文化素材量（个）
        StasticsItem materialItem = new StasticsItem();
        materialItem.setName("文化素材量（个）");
        Integer count = ujcmsMaterialMapper.count(null, null);
        materialItem.setValue(new BigDecimal(count == null ? 0 : count));
        stasticsItems.add(materialItem);
        // 素材使用量（个）
        StasticsItem accessCountItem = new StasticsItem();
        accessCountItem.setName("素材使用量（个）");
        Integer accessCount = orderMapper.count(null, null);
        accessCountItem.setValue(new BigDecimal(accessCount == null ? 0 : accessCount));
        stasticsItems.add(accessCountItem);
        return ResponseEntity.ok(stasticsItems);
    }

    public <T> ResponseEntity<T> verify(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        String md5 = FileUtils.getMd5(inputStream);
        UserMaterialBuy byMd5 = userMaterialBuyMapper.getByMd5(md5);
        if (byMd5 != null) {
            Order order = orderMapper.selectByOrderNo(byMd5.getOrderNo());
            if (order != null) {
                byMd5.setOrder(order);
            }
            return ResponseEntity.ok((T) byMd5);
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body((T) "未找到该素材");
    }

    public ResponseEntity importProgress(String taskId) {
        String key = BATCH_IMPORT_MATERIAL_TASK_CACHE_KEY_PREFIX + taskId;
        String progress = stringRedisTemplate.opsForValue().get(key);
        JSONObject jsonObject = StringUtils.isBlank(progress) ? new JSONObject() : JSONObject.parseObject(progress);
        return ResponseEntity.ok(jsonObject);
    }

    public UjcmsMaterial getMaterialById(Long id) {
        return ujcmsMaterialMapper.selectById(id);
    }

    public void updateUjcmsMaterialBuy(UserMaterialBuy ujcmsMaterialBuy) {
        userMaterialBuyMapper.updateById(ujcmsMaterialBuy);
    }
}
