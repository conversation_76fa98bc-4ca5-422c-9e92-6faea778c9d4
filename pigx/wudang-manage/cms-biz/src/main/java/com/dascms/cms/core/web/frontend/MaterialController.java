package com.dascms.cms.core.web.frontend;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dascms.cms.core.domain.*;
import com.dascms.cms.core.domain.count.RelicTypeCount;
import com.dascms.cms.core.service.*;
import com.dascms.cms.core.support.Constants;
import com.dascms.cms.core.support.LoginUserUtil;
import com.dascms.cms.core.support.Props;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.cms.core.web.support.SiteResolver;
import com.dascms.commons.constants.ChannelConstants;
import com.dascms.commons.web.PathResolver;
import com.dascms.commons.web.exception.Http404Exception;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pig4cloud.pigx.admin.api.entity.SysUser;
import com.pig4cloud.pigx.common.core.constant.CommonConstants;
import com.pig4cloud.pigx.common.core.enums.FileTypeEnum;
import com.pig4cloud.pigx.common.core.util.DateparseUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 前台文章 Controller
 *
 * <AUTHOR>
 */
@Controller("frontendMaterialController")
public class MaterialController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialController.class);

    private final ArticleBufferService bufferService;
    private final ArticleService articleService;
    private final ChannelService channelService;
    private final GroupService groupService;
    private final SiteResolver siteResolver;
    private final PathResolver pathResolver;
    private final Props props;

    @Autowired
    private ArticleRelicAttachService articleRelicAttachService;

    public MaterialController(ArticleBufferService bufferService, ArticleService articleService,
                              ChannelService channelService, GroupService groupService,
                              SiteResolver siteResolver, PathResolver pathResolver, Props props) {
        this.bufferService = bufferService;
        this.articleService = articleService;
        this.channelService = channelService;
        this.groupService = groupService;
        this.siteResolver = siteResolver;
        this.pathResolver = pathResolver;
        this.props = props;
    }

    @GetMapping({UrlConstants.MATERIAL + "/{id}", UrlConstants.MATERIAL + "/{id}/{page:[\\d]+}",
            UrlConstants.MATERIAL + "/{id}/{alias:[\\w-]+}", UrlConstants.MATERIAL + "/{id}/{alias:[\\w-]+}/{page:[\\d]+}",
            "/{subDir:[\\w-]+}" + UrlConstants.MATERIAL + "/{id}", "/{subDir:[\\w-]+}" + UrlConstants.MATERIAL + "/{id}_{page:[\\d]+}",
            "/{subDir:[\\w-]+}" + UrlConstants.MATERIAL + "/{id}/{alias:[\\w-]+}",
            "/{subDir:[\\w-]+}" + UrlConstants.MATERIAL + "/{id}/{alias:[\\w-]+}/{page:[\\d]+}"})
    public String article(@PathVariable Long id, @PathVariable(required = false) String subDir,
                          @PathVariable(required = false) String alias, @PathVariable(required = false) Integer page,
                          @RequestParam(defaultValue = "false") boolean preview,
                          HttpServletRequest request, Map<String, Object> modelMap) {
        Site site = siteResolver.resolve(request, subDir);
        return site.getTheme()+"/article_product_item";
    }

    /**
     * 查询当前登录用户对文章的收藏状态
     *
     * @param article
     */
    private void setArticleFavoriteStatus(Article article, HttpServletRequest request) {
        User user = LoginUserUtil.getLoginUser(request);
        if (user != null) {
            Integer favorite = articleService.getFavorite(user.getId(), article.getId());
            article.setFavorite(favorite == null ? 0 : favorite);
        } else {//没有登录，默认为未收藏
            article.setFavorite(0);
        }
    }

    /**
     * 填充文物详情数据
     *
     * @param article
     */
    private void assignArticle(Article article, Channel channel, Map<String, Object> modelMap) {
        Model articleModel = channel.getArticleModel();
        String customs = articleModel.getCustoms();
        String customsValue = article.getMainsJson();
        if (StringUtils.isNotBlank(customs) && StringUtils.isNotBlank(customsValue)) {
            JSONObject customsJson = JSONObject.parseObject(customsValue);
            try {
                List<Model.Field> fieldList = Constants.MAPPER.readValue(customs, new TypeReference<List<Model.Field>>() {
                });
                for (Model.Field field : fieldList) {
                    Object o = customsJson.get(field.getCode());
                    field.setValue(o);
                    if (ChannelConstants.DATE.equals(field.getType()) && o != null) {
                        try {
                            LocalDateTime o1 = DateparseUtil.parse((String) o);
                            field.setValue(DateUtil.format(o1, CommonConstants.DATE_TIME_FORMAT));
                        } catch (Exception e) {
                            LOGGER.info("time_parse_exception", o.toString());
                            LOGGER.error("time_parse_exception", e);
                        }
                    }
                }
                modelMap.put("fieldList", fieldList);
                //附件列表和统计
                List<ArticleRelicAttach> articleRelicAttaches = articleRelicAttachService.selectByArticleId(article.getId());
                for (ArticleRelicAttach articleRelicAttach : articleRelicAttaches) {
                    articleRelicAttach.setFileTypeLabel(FileTypeEnum.getName(articleRelicAttach.getFileType().toString()));
                }
                articleRelicAttaches = articleRelicAttaches.parallelStream().filter(a -> CommonConstants.ONE.equals(a.getShowFlag())).collect(Collectors.toList());
                modelMap.put("attachList", articleRelicAttaches);
                //类型统计
                Map<Integer, List<ArticleRelicAttach>> integerListMap = articleRelicAttaches.parallelStream().collect(Collectors.groupingBy(ArticleRelicAttach::getFileType));
                List<RelicTypeCount> relicTypeCounts = new LinkedList<>();
                RelicTypeCount all = new RelicTypeCount();
                all.setLabel("全部");
                all.setTypeId(0);
                all.setTotal(0);
                relicTypeCounts.add(all);
                for (Map.Entry<Integer, List<ArticleRelicAttach>> integerListEntry : integerListMap.entrySet()) {
                    RelicTypeCount relicTypeCount = new RelicTypeCount();
                    relicTypeCount.setTypeId(integerListEntry.getKey());
                    relicTypeCount.setLabel(FileTypeEnum.getName(integerListEntry.getKey().toString()));
                    relicTypeCount.setTotal(integerListEntry.getValue().size());
                    all.setTotal(all.getTotal() + relicTypeCount.getTotal());
                    relicTypeCounts.add(relicTypeCount);
                }
                modelMap.put("countList", relicTypeCounts);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }


    private Article validateArticle(Long id, Site site, SysUser user, boolean preview) {
        Article article = articleService.select(id);
        if (article == null) {
            throw new Http404Exception("Article not found. ID=" + id);
        }
        if (!site.getId().equals(article.getSiteId())) {
            throw new Http404Exception("error.notInSite",
                    String.valueOf(article.getSiteId()), String.valueOf(site.getId()));
        }
        //checkAccessPermission(article, user, groupService, channelService, preview);
        return article;
    }
}
