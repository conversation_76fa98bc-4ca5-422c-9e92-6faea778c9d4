package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.domain.SensitiveWord;
import com.dascms.cms.core.service.SensitiveWordService;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.cms.core.web.request.SensitiveWordQry;
import com.pig4cloud.pigx.common.core.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

import static com.dascms.commons.db.MyBatis.springPage;

@Controller
@RequestMapping(UrlConstants.BACKEND_API + "/sensitiveword")
public class SensitiveWordController {

    @Autowired
    private SensitiveWordService sensitiveWordService;

    /**
     * 获取目录列表（分类列表）
     */
    @RequestMapping("pageList")
    @ResponseBody
    public Page pageList(SensitiveWordQry sensitiveWordQry) {
        return springPage(sensitiveWordService.selectPage(sensitiveWordQry));
    }

    /**
     * 添加敏感词
     *
     * @param addSensitiveWord
     * @return
     */
    @RequestMapping("add")
    @ResponseBody
    public R add(@RequestBody SensitiveWord addSensitiveWord) {
        return sensitiveWordService.add(addSensitiveWord);
    }

    /**
     * 更新敏感词
     *
     * @param addSensitiveWord
     * @return
     */
    @RequestMapping("update")
    @ResponseBody
    public R update(@RequestBody SensitiveWord addSensitiveWord) {
        return sensitiveWordService.update(addSensitiveWord);
    }

    /**
     * 敏感词详情
     */
    @RequestMapping("detail")
    @ResponseBody
    public R detail(@RequestParam Long id) {
        return R.ok(sensitiveWordService.select(id));
    }

    /**
     * 删除敏感词
     */

    @RequestMapping("delete")
    @ResponseBody
    public R delete(@RequestBody List<Long> ids) {
        sensitiveWordService.delete(ids);
        return R.ok();
    }
}
