package com.dascms.cms.core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dascms.cms.core.domain.material.UserMaterialDownload;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface UserMaterialDownloadMapper extends BaseMapper<UserMaterialDownload> {

    /**
     * 获取用户收藏的素材
     *
     * @param materialId
     * @param userId
     * @return
     */
    UserMaterialDownload get(@Param("materialId") Long materialId, @Param("userId") Long userId);

    /**
     * 查询用户收藏的素材
     *
     * @param userId
     * @return
     */
    List<Long> getByUserId(@Param("userId") Long userId);

    /**
     * 统计用户下载的素材数量
     * 
     * @param userId
     * @return
     */
    Integer countByUserId(@Param("userId") Long userId);
}
