package com.dascms.cms.core.web.util;

import com.dascms.cms.CmsBackendlication;
import com.dascms.cms.core.domain.Channel;
import com.dascms.cms.core.domain.count.ChannelCount;
import com.dascms.cms.core.service.ArticleService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 栏目辅助工具类
 */
@Component
public class ChannelUtil {
    /**
     * 填充统计结果--汇总到顶层父级id
     *
     * @param list
     */
    public void assembleCount(List<Channel> list) {
        ArticleService articleService = CmsBackendlication.applicationContext.getBean(ArticleService.class);
        List<ChannelCount> channelCounts = articleService.statByChannel();
        //按照父级id汇总
        Map<Long, Integer> channelCountMap = channelCounts.parallelStream().collect(Collectors.toMap(ChannelCount::getChannelId, ChannelCount::getCount));
        for (Channel channel : list) {
            channel.countResult = channelCountMap.get(channel.getId());
        }
    }
}
