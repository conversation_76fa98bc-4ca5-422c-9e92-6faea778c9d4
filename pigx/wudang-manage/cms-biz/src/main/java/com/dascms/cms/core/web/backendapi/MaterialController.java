package com.dascms.cms.core.web.backendapi;

import com.dascms.cms.core.domain.material.UjcmsMaterial;
import com.dascms.cms.core.service.ConfigService;
import com.dascms.cms.core.service.UjcmsMaterialService;
import com.dascms.cms.core.service.args.UjcmsMaterialArgs;
import com.dascms.cms.core.support.Props;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.commons.web.PathResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping(UrlConstants.BACKEND_API + "/material")
public class MaterialController {

    @Autowired
    private UjcmsMaterialService ujcmsMaterialService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private PathResolver pathResolver;

    @Autowired
    private Props props;

    /**
     * 添加素材
     */
    @PostMapping("/create")
    @ResponseBody
    public ResponseEntity<UjcmsMaterial> create(@RequestBody UjcmsMaterial ujcmsMaterial) throws Exception {
        return ujcmsMaterialService.create(ujcmsMaterial);
    }

    /**
     * 批量添加素材
     *
     * @return
     */
    @PostMapping("/create-batch")
    @ResponseBody
    public ResponseEntity<String> createBatch(@RequestBody List<UjcmsMaterial> ujcmsMaterialList) throws Exception {
        return ujcmsMaterialService.createBatch(ujcmsMaterialList);
    }

    /**
     * 修改素材
     */
    @PostMapping("/update")
    @ResponseBody
    public ResponseEntity<UjcmsMaterial> update(@RequestBody UjcmsMaterial ujcmsMaterial) throws Exception {
        return ujcmsMaterialService.update(ujcmsMaterial);
    }

    /**
     * 删除素材
     *
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    public ResponseEntity<String> delete(@RequestBody List<Long> ids) {
        return ujcmsMaterialService.delete(ids);
    }

    /**
     * 素材详情
     *
     * @return
     */
    @GetMapping("/detail")
    @ResponseBody
    public ResponseEntity<UjcmsMaterial> detail(@RequestParam long id) {
        return ujcmsMaterialService.detail(id);
    }


    /**
     * 素材列表
     */
    @GetMapping("/page")
    @ResponseBody
    public ResponseEntity<Page<UjcmsMaterial>> page(UjcmsMaterialArgs args) {
        return ujcmsMaterialService.page(args);
    }

    /**
     * 批量上架-修改上架状态
     */
    @PostMapping("/onsale")
    @ResponseBody
    public ResponseEntity<String> onsale(@RequestBody List<UjcmsMaterialArgs> args) {
        return ujcmsMaterialService.onsale(args);
    }

    /**
     * 首页展示-修改首页展示状态
     */
    @PostMapping("/homePage")
    @ResponseBody
    public ResponseEntity<String> homePage(@RequestBody UjcmsMaterialArgs args) {
        return ujcmsMaterialService.homepage(args.getId(), args.getHomePage());
    }

    /**
     * 查询批量导入进度信息
     */
    @RequestMapping("importProgress")
    @ResponseBody
    public ResponseEntity<String> importProgress(@RequestParam String taskId) {
        return ujcmsMaterialService.importProgress(taskId);
    }
}
