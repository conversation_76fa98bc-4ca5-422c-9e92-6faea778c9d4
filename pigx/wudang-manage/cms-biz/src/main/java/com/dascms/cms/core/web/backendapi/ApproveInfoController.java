package com.dascms.cms.core.web.backendapi;

import com.pig4cloud.pigx.flow.task.entity.Process;
import com.dascms.cms.core.domain.ApproveInfo;
import com.dascms.cms.core.service.ApproveInfoService;
import com.dascms.cms.core.service.args.ApproveInfoArgs;
import com.dascms.cms.core.service.args.ApproveInfoParam;
import com.dascms.cms.core.support.UrlConstants;
import com.dascms.commons.web.Responses.Body;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import static com.dascms.cms.core.support.Constants.validPage;
import static com.dascms.cms.core.support.Constants.validPageSize;
import static com.dascms.commons.db.MyBatis.springPage;
import static com.dascms.commons.query.QueryUtils.getQueryMap;

@RestController("backendApproveInfoController")
@RequestMapping(UrlConstants.BACKEND_API + "/core/approve/info")
public class ApproveInfoController {

    private final ApproveInfoService service;

    public ApproveInfoController(ApproveInfoService approveInfoService) {
        this.service = approveInfoService;
    }

    @GetMapping("/doneList")
    public Page<ApproveInfo> doneList(@RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize,
                                      HttpServletRequest request) {
        ApproveInfoArgs args = ApproveInfoArgs.of(getQueryMap(request.getQueryString()));
        return springPage(service.selectPage(args, validPage(page), validPageSize(pageSize)));
    }


    @PostMapping("/audit")
    public ResponseEntity<Body> audit(@Validated @RequestBody ApproveInfoParam param) {
        return service.audit(param);
    }


    @PostMapping("/rollback")
    public ResponseEntity<Body> rollback(@Validated @RequestBody ApproveInfoParam param) {
        return service.rollback(param);
    }

    @GetMapping("/processPage")
    public Page processPage(@RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Process> processPage = service.selectProcessPage(page, pageSize);
        return new PageImpl<>(processPage.getRecords(), PageRequest.of(page - 1, pageSize), processPage.getTotal());
    }

}
