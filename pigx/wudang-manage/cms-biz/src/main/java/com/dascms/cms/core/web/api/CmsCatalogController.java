package com.dascms.cms.core.web.api;

import com.dascms.cms.core.domain.catalog.Catalog;
import com.dascms.cms.core.service.CataLogService;
import com.dascms.cms.core.service.args.CatalogArgs;
import com.dascms.cms.core.support.UrlConstants;
import com.pig4cloud.pigx.common.core.constant.SecurityConstants;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.controller.request.catalog.CatalogQry;
import com.pig4cloud.pigx.data.center.remote.RemoteCataloService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping(UrlConstants.FRONTEND_API + "/catalog")
public class CmsCatalogController {

    @Autowired
    private RemoteCataloService remoteCataloService;

    @Autowired
    private CataLogService cataLogService;

    /**
     * 获取目录列表（分类列表）
     */
    @RequestMapping("list")
    @ResponseBody
    public R catalogList(@RequestBody CatalogQry catalogQry) {
        return remoteCataloService.catalogList(catalogQry, SecurityConstants.FROM_IN);
    }

    /**
     * 根据目录编号获取父级目录
     */
    @RequestMapping("parent")
    @ResponseBody
    public R parent(@RequestParam String catalogNo) {
        return remoteCataloService.parent(catalogNo);
    }

    /**
     * 添加类目
     */
    @RequestMapping("add")
    @ResponseBody
    public ResponseEntity<Catalog> add(@RequestBody Catalog catalog) {
        return cataLogService.add(catalog);
    }
    
    /**
     * 根据父级类目编号获取子类目
     */
    @RequestMapping("child")
    @ResponseBody
    public ResponseEntity<List<Catalog>> child(CatalogArgs args) {
        return cataLogService.list(args);
    }

    /**
     * 编辑类目
     */
    @RequestMapping("edit")
    @ResponseBody
    public ResponseEntity<Catalog> edit(@RequestBody Catalog catalog) {
        return cataLogService.edit(catalog);
    }

    /**
     * 删除类目
     */
    @RequestMapping("delete")
    @ResponseBody
    public ResponseEntity<String> delete(@RequestBody Catalog catalog) {
        return cataLogService.delete(catalog);
    }
}
