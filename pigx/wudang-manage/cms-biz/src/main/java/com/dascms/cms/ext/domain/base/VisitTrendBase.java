package com.dascms.cms.ext.domain.base;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * This class was generated by MyBatis Generator.
 *
 * <AUTHOR> Generator
 */
public class VisitTrendBase extends Model<VisitTrendBase> {
    private static final long serialVersionUID = 1L;

    /**
     * 数据库表名
     */
    public static final String TABLE_NAME = "visit_trend";

    /**
     * 访问_趋势ID
     */
    @NotNull
    @Schema(description="访问_趋势ID")
    private Long id = 0L;

    /**
     * 站点ID
     */
    @NotNull
    @Schema(description="站点ID")
    private Long siteId = 0L;

    /**
     * 访问量
     */
    @NotNull
    @Schema(description="访问量")
    private Long pvCount = 0L;

    /**
     * 访客数
     */
    @NotNull
    @Schema(description="访客数")
    private Long uvCount = 0L;

    /**
     * IP数
     */
    @NotNull
    @Schema(description="IP数")
    private Long ipCount = 0L;

    /**
     * 新访客数
     */
    @NotNull
    @Schema(description="新访客数")
    private Long nvCount = 0L;

    /**
     * 跳出数
     */
    @NotNull
    @Schema(description="跳出数")
    private Integer bounceCount = 0;

    /**
     * 访问时长
     */
    @NotNull
    @Schema(description="访问时长")
    private Integer duration = 0;

    /**
     * 统计日期(yyyyMMddHHmm)
     */
    @Length(max = 12)
    @NotNull
    @Schema(description="统计日期(yyyyMMddHHmm)")
    private String dateString = "";

    /**
     * 统计周期(1:分,2:时,3:日,4:月)
     */
    @NotNull
    @Schema(description="统计周期(1:分,2:时,3:日,4:月)")
    private Short period = 1;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getPvCount() {
        return pvCount;
    }

    public void setPvCount(Long pvCount) {
        this.pvCount = pvCount;
    }

    public Long getUvCount() {
        return uvCount;
    }

    public void setUvCount(Long uvCount) {
        this.uvCount = uvCount;
    }

    public Long getIpCount() {
        return ipCount;
    }

    public void setIpCount(Long ipCount) {
        this.ipCount = ipCount;
    }

    public Long getNvCount() {
        return nvCount;
    }

    public void setNvCount(Long nvCount) {
        this.nvCount = nvCount;
    }

    public Integer getBounceCount() {
        return bounceCount;
    }

    public void setBounceCount(Integer bounceCount) {
        this.bounceCount = bounceCount;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public Short getPeriod() {
        return period;
    }

    public void setPeriod(Short period) {
        this.period = period;
    }
}