server:
  port: 4015

spring:
  application:
    name: cms-biz
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        #server-addr: ${NACOS_HOST:pigx-register}:${NACOS_PORT:8848}
        server-addr: ************:8848
        namespace: das-geotrellis
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: das-geotrellis
  config:
    import:
      - optional:nacos:application-dev.yml
      - optional:nacos:${spring.application.name}-dev.yml



