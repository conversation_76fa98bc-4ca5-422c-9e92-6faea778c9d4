package com.wds;

import com.alibaba.fastjson.JSON;
import com.pig4cloud.pigx.admin.api.dto.UserDTO;
import com.pig4cloud.pigx.admin.api.entity.SysUser;
import org.apache.commons.beanutils.BeanUtils;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;

public class Imagetest {

    @Test
    public void beanCopy() throws InvocationTargetException, IllegalAccessException {
        UserDTO userDTO = new UserDTO();
        userDTO.setUsername("admin");
        userDTO.setPassword("123456");
        SysUser sysUser = new SysUser();
        sysUser.setUserId(1L);
        BeanUtils.copyProperties(sysUser,userDTO);
        System.err.println(JSON.toJSONString(sysUser));
        String path="/1/123/";
        System.err.println(path.replace("/1/","/2/"));
    }

}
