package com.wds;

import com.dascms.cms.core.domain.Config;
import com.dascms.cms.core.support.Constants;
import org.junit.jupiter.api.Test;

public class JsonTest {
    @Test
    public void test() throws Exception {
        String json = "{\"imageTypes\":\"jpg,jpeg,png,gif\",\"videoTypes\":\"mp4,m3u8\",\"docTypes\":\"pdf,doc,docx,xls,xlsx,ppt,pptx\",\"fileTypes\":\"zip,7z,gz,bz2,iso,rar,pdf,doc,docx,xls,xlsx,ppt,pptx\",\"imageLimit\":0,\"videoLimit\":0,\"docLimit\":0,\"fileLimit\":0,\"imageMaxWidth\":1920,\"imageMaxHeight\":0,\"videoLimitByte\":0,\"imageLimitByte\":0,\"docLimitByte\":0,\"fileLimitByte\":0,\"docInputAccept\":\".zip,.7z,.gz,.bz2,.iso,.rar,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx\",\"videoInputAccept\":\".mp4,.m3u8\",\"fileInputAccept\":\".zip,.7z,.gz,.bz2,.iso,.rar,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx\",\"imageInputAccept\":\".jpg,.jpeg,.png,.gif\"}";
        Config.Upload upload = Constants.MAPPER.readValue(json, Config.Upload.class);
        System.err.println(upload);
        String overview="1024x768 或 1280x720";
        //按照或分割字符串，需要先删除中间的空格
        overview = overview.replaceAll(" ", "");
        String[] split = overview.split("或");
        for (String s : split) {
            System.err.println(s);
        }
    }
}
